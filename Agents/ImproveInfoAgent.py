from langchain_core.output_parsers import PydanticOutputParser
from langchain_core.prompts import SystemMessagePromptTemplate, HumanMessagePromptTemplate, \
    ChatPromptTemplate
from langchain_core.runnables import <PERSON><PERSON><PERSON><PERSON><PERSON>b<PERSON>
from langchain_core.runnables.history import RunnableWithMessageHistory

from Configs.Config import SysConfig
from LLM.LLMManager import sys_llm_manager
from Models.agent.TalentInfoProcessing import ContentInfo
from Utils.CommonUtils import remove_think_tags
from Utils.logs.LoggingConfig import logger


class ImproveInfoAgent:
    _llm_name: str | None = "DEFAULT"

    def __init__(self):
        self.__temperature = SysConfig["agents"]["kb_agent"]["temperature"]
        self.__output_improve_info = PydanticOutputParser(pydantic_object=ContentInfo)

    # 根据LLM的名字设置LLM相关信息，包含对话信息
    def __set_llm(self, output_parser: PydanticOutputParser, system_template: SystemMessagePromptTemplate,
                  human_template: HumanMessagePromptTemplate) -> RunnableWithMessageHistory | None:
        llm_helper = sys_llm_manager.get_generate_use_llm_helper()
        if llm_helper is None:
            return None
        llm_obj = llm_helper.get_llm_object(self.__temperature)

        prompt_template = ChatPromptTemplate.from_messages([
            system_template,
            human_template
        ]).partial(format_instructions=output_parser.get_format_instructions())

        runnable = (prompt_template | llm_obj
                    | RunnableLambda(lambda x: x if isinstance(x, str) else x.content)
                    | RunnableLambda(remove_think_tags)
                    | output_parser)
        return runnable

    # 一键完善
    async def evaluateImproveInfo(self, evaluate):
        system_template = SystemMessagePromptTemplate.from_template(
            """
            你是一位经验丰富的面试评价优化专家，长期从事面试评价的撰写和优化工作。你熟悉面试评价的写作规范，能够从面试官的角度出发，精准地识别评价中的优点和不足并进行润色优化。
            - 规则介绍: 
                - 根据原始评价的真实语义进行专业润色，绝不可拔高或弱化原有评价等级。  
                - 若原始评价已给出明确等级（如“一般 / 较差 / 良好”），必须保持该等级不变。
                - 仔细阅读现有的面试评价内容，对评价内容中的优点和不足进行优化，使其更加专业和客观。
                - 从面试官的角度出发，对评价内容进行润色和优化。确保优化后的评价内容逻辑清晰，表达准确。
                - 确保评价内容从面试官的角度出发，突出面试者的整体表现和专业能力
                - 你必须精准地识别评价内容的优点和不足，从面试官的角度出发去润色内容。
                - 输出的内容中不允许带有 '综合评价' 标题字样。
            输出格式：{format_instructions}
            /no_think
            """
        )
        human_template = HumanMessagePromptTemplate.from_template(
            "综合评价：{evaluate}"
        )
        # 调用LLM进行解析
        logger.info(f"内容：{evaluate}")
        talent_info = self.__set_llm(self.__output_improve_info, system_template, human_template).invoke(
            {"evaluate": evaluate}
        )
        logger.info(f"结果：{talent_info}")
        return talent_info

    async def hireImproveInfo(self, position, content, interviewRounds) -> ContentInfo:
        try:
            system_template = SystemMessagePromptTemplate.from_template(
                """
                你是一位经验丰富的人力资源管理专家，精通岗位要求指定与人才选拔，对不同岗位的技能要求和素质标准有着深入的理解，能更好的明确岗位招聘要求。
                技能:
                    你具备出色的分析能力和扎实的人力资源管理知识，能够根据不同的岗位来完善不同的要求，对岗位选拔、招聘要求制定极为擅长。
                约束：
                    - 需要根据用户给出的招聘岗位、未完善的岗位要求来结合完善并优化要求。
                    - 若未完善的岗位要求为空，则只根据招聘岗位进行完善要求。
                    - 补充的信息中只输出要求且必须要贴合招聘岗位。
                输出格式：{format_instructions}
                /no_think
               """
            )
            human_template = HumanMessagePromptTemplate.from_template(
                "招聘岗位：{position}"
                "岗位要求：{content}"
            )
            # 调用LLM进行解析
            logger.info(f"内容：{position}, {content}")
            talent_info = self.__set_llm(self.__output_improve_info, system_template, human_template).invoke(
                {"position": position, "content": content}
            )
            logger.info(f"结果：{talent_info}")
            return talent_info
        except Exception as e:
            print(f"格式化失败: {str(e)}")
            raise e
