{"description": "Ollama 并发优化配置示例", "async_config": {"max_llm_workers": 8, "max_file_workers": 4, "max_download_workers": 6, "ollama_specific": {"max_pool_size_per_model": 15, "connection_timeout": 60, "keep_alive_duration": "10m", "max_concurrent_requests": 10, "request_timeout": 120, "retry_attempts": 3, "retry_delay": 1.0}}, "server": {"workers": 2, "limit_concurrency": 200, "limit_max_requests": 2000}, "ollama_models": [{"name": "llama3.1:8b", "concurrent_config": {"max_concurrent_chats": 5, "pool_size": 10, "temperature_range": [0.1, 1.0], "context_sizes": [4096, 8192, 16384]}}, {"name": "qwen2.5:7b", "concurrent_config": {"max_concurrent_chats": 4, "pool_size": 8, "temperature_range": [0.1, 0.9], "context_sizes": [4096, 8192]}}, {"name": "gemma2:9b", "concurrent_config": {"max_concurrent_chats": 3, "pool_size": 6, "temperature_range": [0.2, 0.8], "context_sizes": [4096, 8192]}}], "performance_tuning": {"enable_connection_pooling": true, "enable_request_batching": true, "batch_size": 5, "batch_timeout": 2.0, "enable_response_caching": false, "cache_ttl": 300, "enable_health_monitoring": true, "health_check_interval": 30, "enable_metrics_collection": true, "metrics_export_interval": 60}, "monitoring": {"log_level": "INFO", "enable_performance_logging": true, "enable_error_tracking": true, "enable_connection_pool_monitoring": true, "alert_thresholds": {"response_time_ms": 5000, "error_rate_percent": 5.0, "pool_utilization_percent": 90.0, "queue_length": 50}}, "fallback_config": {"enable_fallback": true, "fallback_models": ["llama3.1:8b", "qwen2.5:7b"], "fallback_timeout": 10, "max_fallback_attempts": 2}}