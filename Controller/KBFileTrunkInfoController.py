import re
import uuid
from typing import Optional

from fastapi import APIRouter, Depends, Query, Path
from fastapi.encoders import jsonable_encoder
from pydantic import BaseModel

from Models.AjaxResult import AjaxResult
from Models.pojo import KBFileTrunkInfo
from Models.pojo.KBFileTrunkInfo import KBFileTrunkInfo
from Models.pojo.KBFileInfo import KBFileInfo
from Services.KBTrunkVectorService.CustomBM25Retriever import chinese_keywords
from Services.KBTrunkVectorService.TrunkVectorService import TrunkVectorSaveService
from Services.SqlServer.KBFileTrunkInfoService import KBFileTrunkInfoService
from Services.SqlServer.KBFileInfoService import KBFileInfoService
from Utils.CommonUtils import list_to_str

router = APIRouter(prefix="/agentService/api/kbFileTrunk", tags=["kbFileTrunk"])


# 获取服务实例
def get_trunk_service():
    return KBFileTrunkInfoService()
# 获取服务实例
def get_file_service():
    return KBFileInfoService()


'''
获取文件分块列表
'''


@router.get("/file/trunks/{id}")
def get_file_trunk_list(
        id: str = Path(..., title="文件ID"),
        trunkState: Optional[int] = Query(None, title="分块状态"),
        content: Optional[str] = Query(None, title="分块内容"),
        pageNum: int = Query(1, alias="pageNum", ge=1, title="页码"),
        pageSize: int = Query(10, alias="pageSize", ge=1, le=100, title="每页数量"),
        service: KBFileTrunkInfoService = Depends(get_trunk_service)
):
    try:
        params = KBFileTrunkInfo()
        params.file_id = id
        if trunkState is not None and trunkState != "":
            params.state = trunkState
        if content and content.strip():
            # 转义特殊字符
            safe_content = content.strip().replace("%", "\\%").replace("_", "\\_")
            # 处理中间连续空格
            safe_content = re.sub(r'\s+', '%', safe_content)
            params.content = f"%{safe_content}%"

        list, total = service.select_all(params, pageNum, pageSize)
        return AjaxResult.success_for_rows(total, jsonable_encoder([trunk.to_dict() for trunk in list]))
    except Exception as e:
        print(f"获取文件分块列表失败: {str(e)}")
        return AjaxResult.error(str(e))


'''
获取文件分块添加
'''


class KBFileTrunkAddRequest(BaseModel):
    file_id: str = None
    kb_id: int = None
    content: str = None
    keywords: str = None
    meta_data: dict = None


@router.post("/addTrunk")
def add_trunk(trunk_info: KBFileTrunkInfo, service: KBFileTrunkInfoService = Depends(get_trunk_service)):
    try:
        # 添加默认值
        trunk_info.id = str(uuid.uuid4())
        trunk_info.status = 0
        # 文件ID不能为空
        if trunk_info.file_id is None:
            return AjaxResult.error("文件ID不能为空")
        # 知识库ID不能为空
        if trunk_info.kb_id is None:
            return AjaxResult.error("知识库ID不能为空")
        # 文件内容不能为空
        if trunk_info.content is None or len(trunk_info.content) == 0:
            return AjaxResult.error("文件内容不能为空")
        # 查询最大序号
        sort_num = service.get_max_sort(trunk_info.file_id)
        trunk_info.sort = sort_num + 1
        trunk_info.keywords += list_to_str(chinese_keywords(trunk_info.content))
        result = service.insert(trunk_info)
        if result is None:
            return AjaxResult.error("文件分块添加失败")
        # 更新文件分块数量
        count = service.count_by_file_id(file_id=trunk_info.file_id)
        file = KBFileInfo(file_id=trunk_info.file_id,trunk_count=count)
        get_file_service().update(file)

        TrunkVectorSaveService.save_trunks([trunk_info])
        return AjaxResult.success(data={"id": trunk_info.id, "add_trunk": trunk_info})
    except Exception as e:
        print(f"文件分块添加时出错: {str(e)}")
        raise AjaxResult.handle_exception(e)


'''
文件分块编辑
'''


@router.post("/updateTrunk")
def update_trunk(request: KBFileTrunkInfo, service: KBFileTrunkInfoService = Depends(get_trunk_service)):
    try:
        result = service.update(request)
        if result is None:
            return AjaxResult.error("文件分块编辑失败")
        TrunkVectorSaveService.update_trunk(request)
        return AjaxResult.success(data={"id": request.id, "row": result})
    except Exception as e:
        print(f"文件分块编辑时出错: {str(e)}")
        raise AjaxResult.handle_exception(e)


'''
删除文件分块
'''


@router.delete("/deleteTrunk")
def remove_file_data(
        # 使用Query参数接收数组（多个同名参数）
        ids: str = None,
        service: KBFileTrunkInfoService = Depends(get_trunk_service)
):
    try:
        if not ids:
            return AjaxResult.error("至少需要提供一个删除条件")
        # 查询文件分块所属的文件ID
        trunk_info = service.select_by_id(ids)
        # 删除文件分块
        result = service.batch_delete(
            ids=ids
        )
        if result is None:
            return AjaxResult.error("文件分块删除失败")
        # 更新文件分块数量
        count = service.count_by_file_id(file_id=trunk_info.file_id)
        file = KBFileInfo(id=trunk_info.file_id,trunk_count=count)
        get_file_service().update(file)
        for id in ids:
            TrunkVectorSaveService.delete_by_file_id(id)
        return AjaxResult.success({"deleted_count": result})
    except Exception as e:
        return AjaxResult.error(str(e))


@router.get("/getTrunk/{id}")
def get_trunk(id: str = Path(..., title="文件ID"), service: KBFileTrunkInfoService = Depends(get_trunk_service)):
    """
    搜索文件分块
    :param id: 文件ID
    :return:
    """
    try:
        if id is None:
            return AjaxResult.error("文件ID不能为空")
        result = service.select_by_id(id)
        return AjaxResult.success(data=result)
    except Exception as e:
        print(f"获取文件分块失败: {str(e)}")
        return AjaxResult.error(str(e))
    pass


# 更新文件分块状态
@router.get("/updateTrunkState/{id}/{status}")
def update_trunk_status(
        id: str = Path(..., title="ID"),
        status: int = Path(..., title="状态"),
        service: KBFileTrunkInfoService = Depends(get_trunk_service)):
    try:
        if id is None:
            return AjaxResult.error("文件ID不能为空")
        if status is None:
            return AjaxResult.error("文件状态不能为空")
        result = service.update_status(id, status)
        # 查询文件分块
        trunk = service.select_by_id(id)
        trunk.status = status
        TrunkVectorSaveService.update_trunk(trunk)
        if result is None:
            return AjaxResult.error("文件分块状态更新失败")
        return AjaxResult.success(data=result)
    except Exception as e:
        print(f"更新文件分块状态失败: {str(e)}")
        return AjaxResult.error(str(e))
