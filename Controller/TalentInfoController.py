from urllib.parse import unquote

from fastapi import APIRouter
from openai import BaseModel

from Agents.AsyncImproveInfoAgent import AsyncImproveInfoAgent
from Agents.AsyncTalentAgent import AsyncTalentAgent
from Models.AjaxResult import AjaxResult
from Models.dto.TaskInfoDto import TaskInfoDto
from Services.TaskServer.AsyncTalentParseWorker import AsyncTalentParseWorker
from Utils.logs.LoggingConfig import logger

# 添加项目根目录到系统路径
router = APIRouter(prefix="/agentService/api/talent", tags=["talent"])


class TalentInfo(BaseModel):
    id: int = None
    content: str = None
    question: str = None


@router.post("/info")
async def chat(request: TalentInfo):
    try:
        if not request.question:
            raise AjaxResult.error()

        # 使用异步代理，避免阻塞其他接口
        async_agent = AsyncTalentAgent()
        row = await async_agent.chat_for_answer_async(request.question, request.id)
        return AjaxResult.success(row)

    except Exception as e:
        logger.error(f"Error in chat: {str(e)}")
        return AjaxResult.error(data=0, message=str(e))


# 简历解析
@router.get("/parse")
async def parse(fileUrl: str = None):
    try:
        if not fileUrl:
            raise AjaxResult.error("文件地址不能为空")

        # 使用异步文件解析器，避免阻塞
        async_parser = AsyncTalentParseWorker(fileUrl)
        async_agent = AsyncTalentAgent()

        content = await async_parser._task()
        if not content:
            raise AjaxResult.error("文件解析失败")

        talent_info = await async_agent.formatting_async(content)
        return AjaxResult.success(talent_info)

    except Exception as e:
        logger.error(f"Error in parse: {str(e)}")
        return AjaxResult.error(str(e))


# 简历分析
@router.get("/analyse")
async def analyse(fileUrl: str = None, taskId: int = None, id: int = None):
    async_agent = None
    try:
        if not fileUrl:
            raise AjaxResult.error("文件地址不能为空")

        # 使用异步代理，避免阻塞
        async_agent = AsyncTalentAgent()
        async_parser = AsyncTalentParseWorker(fileUrl)

        # 标记解析中状态=1
        if taskId is not None:
            await async_agent.update_file_status_async(id, 1)

        # 文件解析
        content = await async_parser._task()
        if not content:
            # 解析失败，标记失败状态=2
            if taskId is not None:
                await async_agent.update_file_status_async(id, 2)
            raise AjaxResult.error("文件解析失败")

        # 格式化并评分
        talent_info = await async_agent.formatting_async(content)
        if talent_info is None:
            # 解析失败，标记失败状态=2
            if taskId is not None:
                await async_agent.update_file_status_async(id, 2)
            raise AjaxResult.error("简历解析失败")

        task_dto = await async_agent.get_analyse_task_info_async(taskId)
        if not task_dto:
            if taskId is not None:
                await async_agent.update_file_status_async(id, 2)
            raise AjaxResult.error("获取岗位信息失败，无法评分")

        taskInfo = TaskInfoDto.from_dict(task_dto)
        total_score = await async_agent.calculate_total_score_async(talent_info, taskInfo)
        talent_info.totalScore = total_score

        talent_info.fileId = id
        talent_info.jobId = taskId

        # 保存解析结果
        save_success = await async_agent.save_analyse_task_info_async(talent_info)

        # 根据保存结果更新状态
        if taskId is not None:
            if save_success:
                # 保存成功，更新状态为成功=0
                await async_agent.update_file_status_async(id, 0)
            else:
                # 保存失败，更新状态为失败=2
                await async_agent.update_file_status_async(id, 2)
                raise AjaxResult.error("保存分析结果失败")

        return AjaxResult.success(talent_info)

    except Exception as e:
        # 异常统一标记失败
        if async_agent and taskId is not None:
            try:
                await async_agent.update_file_status_async(id, 2)
            except Exception:
                pass
        logger.error(f"Error in analyse: {str(e)}")
        return AjaxResult.error(str(e))

@router.get("/improveInfo")
async def improveInfo(evaluate: str):
    evaluate = unquote(evaluate)
    try:
        if not evaluate:
            raise AjaxResult.error()

        # 使用异步代理，避免阻塞
        async_agent = AsyncImproveInfoAgent()
        body = await async_agent.evaluate_improve_info_async(evaluate)
        return AjaxResult.success(body)

    except Exception as e:
        logger.error(f"Error in improveInfo: {str(e)}")
        return AjaxResult.error(data=0, message="完善信息失败")