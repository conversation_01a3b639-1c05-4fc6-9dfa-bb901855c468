"""
并发 Ollama 使用示例
演示如何使用优化后的 OllamaLLMHelper 进行并发聊天操作
"""

import asyncio
import time
from typing import List

from LLM.LLMManager import sys_llm_manager
from LLM.AsyncOllamaLLMHelper import AsyncOllamaLLMHelper
from Models.peewee.OrmModel import KBModel
from Utils.logs.LoggingConfig import logger


async def basic_async_chat_example():
    """基础异步聊天示例"""
    print("=== 基础异步聊天示例 ===")
    
    # 获取异步 Ollama Helper
    async_helper = sys_llm_manager.get_async_ollama_helper()
    
    if async_helper is None:
        print("未找到 Ollama 模型配置")
        return
    
    # 单个异步聊天
    start_time = time.time()
    response = await async_helper.chat_async("你好，请介绍一下你自己")
    end_time = time.time()
    
    print(f"响应时间: {end_time - start_time:.2f}秒")
    print(f"回复: {response}")
    print()


async def concurrent_chat_example():
    """并发聊天示例"""
    print("=== 并发聊天示例 ===")
    
    async_helper = sys_llm_manager.get_async_ollama_helper()
    
    if async_helper is None:
        print("未找到 Ollama 模型配置")
        return
    
    # 准备多个问题
    questions = [
        "什么是人工智能？",
        "Python 有哪些优势？",
        "如何学习机器学习？",
        "什么是深度学习？",
        "解释一下神经网络"
    ]
    
    print(f"同时处理 {len(questions)} 个问题...")
    
    # 批量并发处理
    start_time = time.time()
    responses = await async_helper.batch_chat_async(questions, max_concurrent=3)
    end_time = time.time()
    
    print(f"总处理时间: {end_time - start_time:.2f}秒")
    print(f"平均每个问题: {(end_time - start_time) / len(questions):.2f}秒")
    
    for i, (question, response) in enumerate(zip(questions, responses)):
        print(f"\n问题 {i+1}: {question}")
        print(f"回答: {response[:100]}..." if len(response) > 100 else f"回答: {response}")
    print()


async def performance_benchmark_example():
    """性能基准测试示例"""
    print("=== 性能基准测试示例 ===")
    
    async_helper = sys_llm_manager.get_async_ollama_helper()
    
    if async_helper is None:
        print("未找到 Ollama 模型配置")
        return
    
    # 准备测试消息
    test_messages = [
        "计算 2+2",
        "什么是 Python？",
        "解释机器学习",
        "什么是 API？",
        "编程的好处是什么？"
    ]
    
    print("开始性能基准测试...")
    
    # 运行基准测试
    benchmark_results = await async_helper.benchmark_async(
        test_messages, 
        concurrent_levels=[1, 2, 4]
    )
    
    print(f"模型: {benchmark_results['model_name']}")
    print("\n基准测试结果:")
    
    for level, stats in benchmark_results['benchmark_results'].items():
        print(f"\n{level}:")
        print(f"  总时间: {stats['total_time']:.2f}秒")
        print(f"  平均每消息: {stats['avg_time_per_message']:.2f}秒")
        print(f"  每秒处理消息数: {stats['messages_per_second']:.2f}")
        print(f"  成功响应: {stats['successful_responses']}")
        print(f"  错误数量: {stats['error_count']}")
    
    print(f"\n连接池统计: {benchmark_results['pool_stats']}")
    print()


async def health_check_example():
    """健康检查示例"""
    print("=== 健康检查示例 ===")
    
    async_helper = sys_llm_manager.get_async_ollama_helper()
    
    if async_helper is None:
        print("未找到 Ollama 模型配置")
        return
    
    # 执行健康检查
    health_status = await async_helper.health_check_async()
    
    print(f"健康状态: {health_status['status']}")
    print(f"模型: {health_status['model_name']}")
    print(f"API地址: {health_status['base_url']}")
    print(f"响应时间: {health_status['response_time']:.2f}秒")
    
    if health_status['status'] == 'healthy':
        print(f"测试响应长度: {health_status['test_response_length']}")
    else:
        print(f"错误信息: {health_status.get('error', 'Unknown error')}")
    
    print(f"连接池统计: {health_status['pool_stats']}")
    print()


async def stream_chat_example():
    """流式聊天示例"""
    print("=== 流式聊天示例 ===")
    
    async_helper = sys_llm_manager.get_async_ollama_helper()
    
    if async_helper is None:
        print("未找到 Ollama 模型配置")
        return
    
    print("发送流式聊天请求...")
    
    # 流式聊天
    start_time = time.time()
    response = await async_helper.chat_async(
        "请写一首关于春天的短诗", 
        stream=True
    )
    end_time = time.time()
    
    print(f"流式响应时间: {end_time - start_time:.2f}秒")
    print(f"诗歌内容:\n{response}")
    print()


def connection_pool_stats_example():
    """连接池统计示例"""
    print("=== 连接池统计示例 ===")
    
    # 获取所有连接池统计
    all_stats = sys_llm_manager.get_all_pool_stats()
    
    print("同步 LLM Helpers:")
    for key, stats in all_stats['sync_helpers'].items():
        print(f"  模型 {key}: {stats}")
    
    print("\n异步 LLM Helpers:")
    for key, stats in all_stats['async_helpers'].items():
        print(f"  模型 {key}: {stats}")
    
    print()


async def main():
    """主函数，运行所有示例"""
    print("🚀 并发 Ollama 功能演示")
    print("=" * 50)
    
    try:
        # 基础功能
        await basic_async_chat_example()
        
        # 并发功能
        await concurrent_chat_example()
        
        # 性能测试
        await performance_benchmark_example()
        
        # 健康检查
        await health_check_example()
        
        # 流式聊天
        await stream_chat_example()
        
        # 连接池统计
        connection_pool_stats_example()
        
        print("✅ 所有示例运行完成！")
        
    except Exception as e:
        logger.error(f"示例运行出错: {e}")
        print(f"❌ 运行出错: {e}")
    
    finally:
        # 清理连接池
        sys_llm_manager.clear_connection_pools()
        print("🧹 连接池已清理")


if __name__ == "__main__":
    # 运行示例
    asyncio.run(main())
