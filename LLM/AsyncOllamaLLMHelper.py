import asyncio
import time
from typing import List, Dict, Any, Optional, Callable
from concurrent.futures import Thr<PERSON><PERSON>oolExecutor, as_completed
from langchain_core.language_models import BaseLLM, BaseChatModel
from langchain_core.messages import BaseMessage, HumanMessage, AIMessage

from LLM.OllamaLLMHelper import <PERSON>llama<PERSON><PERSON>Helper
from Models.peewee.OrmModel import KBModel
from Utils.AsyncConfig import async_config
from Utils.logs.LoggingConfig import logger


class AsyncOllamaLLMHelper(OllamaLLMHelper):
    """
    异步版本的 OllamaLLMHelper
    提供高级并发聊天功能，支持批量处理、流式响应等
    """

    def __init__(self, model: KBModel = None):
        super().__init__(model)
        self._active_sessions: Dict[str, dict] = {}
        self._session_lock = asyncio.Lock()

    async def chat_async(self, message: str, session_id: str = None, 
                        temperature: float = 0.7, num_ctx: int = None,
                        stream: bool = False) -> str:
        """
        异步聊天方法
        
        Args:
            message: 用户消息
            session_id: 会话ID，用于维护上下文
            temperature: 温度参数
            num_ctx: 上下文长度
            stream: 是否流式返回
            
        Returns:
            AI回复内容
        """
        try:
            if stream:
                return await self._chat_stream_async(message, session_id, temperature, num_ctx)
            else:
                return await self._chat_normal_async(message, session_id, temperature, num_ctx)
        except Exception as e:
            logger.error(f"Async chat error: {e}")
            raise

    async def _chat_normal_async(self, message: str, session_id: str, 
                               temperature: float, num_ctx: int) -> str:
        """普通异步聊天"""
        def _sync_chat():
            llm = self.get_llm_chat_object(temperature, num_ctx)
            messages = [HumanMessage(content=message)]
            response = llm.invoke(messages)
            return response.content if hasattr(response, 'content') else str(response)
        
        return await async_config.run_in_llm_executor(_sync_chat)

    async def _chat_stream_async(self, message: str, session_id: str,
                               temperature: float, num_ctx: int) -> str:
        """流式异步聊天"""
        def _sync_stream_chat():
            llm = self.get_llm_chat_object(temperature, num_ctx)
            messages = [HumanMessage(content=message)]
            
            # 收集流式响应
            full_response = ""
            for chunk in llm.stream(messages):
                if hasattr(chunk, 'content'):
                    full_response += chunk.content
                else:
                    full_response += str(chunk)
            
            return full_response
        
        return await async_config.run_in_llm_executor(_sync_stream_chat)

    async def batch_chat_async(self, messages: List[str], 
                             temperature: float = 0.7, num_ctx: int = None,
                             max_concurrent: int = None) -> List[str]:
        """
        批量异步聊天
        
        Args:
            messages: 消息列表
            temperature: 温度参数
            num_ctx: 上下文长度
            max_concurrent: 最大并发数，默认使用配置的LLM工作线程数
            
        Returns:
            回复列表，顺序与输入消息对应
        """
        if max_concurrent is None:
            max_concurrent = async_config.max_llm_workers
        
        # 使用信号量控制并发
        semaphore = asyncio.Semaphore(max_concurrent)
        
        async def _process_single_message(msg: str, index: int) -> tuple:
            async with semaphore:
                try:
                    response = await self.chat_async(msg, temperature=temperature, num_ctx=num_ctx)
                    return index, response
                except Exception as e:
                    logger.error(f"Batch chat error for message {index}: {e}")
                    return index, f"Error: {str(e)}"
        
        # 创建任务列表
        tasks = [
            _process_single_message(msg, i) 
            for i, msg in enumerate(messages)
        ]
        
        # 等待所有任务完成
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 按原始顺序排序结果
        sorted_results = sorted(results, key=lambda x: x[0] if isinstance(x, tuple) else 0)
        
        return [result[1] if isinstance(result, tuple) else f"Error: {str(result)}" 
                for result in sorted_results]

    async def chat_with_context_async(self, message: str, context_messages: List[BaseMessage],
                                    temperature: float = 0.7, num_ctx: int = None) -> str:
        """
        带上下文的异步聊天
        
        Args:
            message: 当前用户消息
            context_messages: 历史消息上下文
            temperature: 温度参数
            num_ctx: 上下文长度
            
        Returns:
            AI回复内容
        """
        def _sync_context_chat():
            llm = self.get_llm_chat_object(temperature, num_ctx)
            
            # 构建完整的消息列表
            all_messages = context_messages + [HumanMessage(content=message)]
            
            response = llm.invoke(all_messages)
            return response.content if hasattr(response, 'content') else str(response)
        
        return await async_config.run_in_llm_executor(_sync_context_chat)

    async def generate_async(self, prompt: str, temperature: float = 0.7, 
                           num_ctx: int = None) -> str:
        """
        异步文本生成
        
        Args:
            prompt: 生成提示
            temperature: 温度参数
            num_ctx: 上下文长度
            
        Returns:
            生成的文本
        """
        def _sync_generate():
            llm = self.get_llm_object(temperature, num_ctx)
            response = llm.invoke(prompt)
            return response
        
        return await async_config.run_in_llm_executor(_sync_generate)

    async def health_check_async(self) -> dict:
        """
        异步健康检查
        
        Returns:
            健康状态信息
        """
        start_time = time.time()
        
        try:
            # 发送简单的测试消息
            test_response = await self.chat_async("Hello", temperature=0.1)
            
            end_time = time.time()
            response_time = end_time - start_time
            
            return {
                "status": "healthy",
                "model_name": self.get_model_name(),
                "base_url": self._model.api_url,
                "response_time": response_time,
                "test_response_length": len(test_response),
                "pool_stats": self.get_pool_stats()
            }
        except Exception as e:
            end_time = time.time()
            response_time = end_time - start_time
            
            return {
                "status": "unhealthy",
                "model_name": self.get_model_name(),
                "base_url": self._model.api_url,
                "response_time": response_time,
                "error": str(e),
                "pool_stats": self.get_pool_stats()
            }

    async def benchmark_async(self, test_messages: List[str], 
                            concurrent_levels: List[int] = None) -> dict:
        """
        异步性能基准测试
        
        Args:
            test_messages: 测试消息列表
            concurrent_levels: 并发级别列表，默认 [1, 2, 4, 8]
            
        Returns:
            基准测试结果
        """
        if concurrent_levels is None:
            concurrent_levels = [1, 2, 4, 8]
        
        results = {}
        
        for level in concurrent_levels:
            logger.info(f"Running benchmark with concurrency level: {level}")
            
            start_time = time.time()
            
            # 限制并发数进行测试
            responses = await self.batch_chat_async(
                test_messages, 
                max_concurrent=level
            )
            
            end_time = time.time()
            total_time = end_time - start_time
            
            results[f"concurrency_{level}"] = {
                "total_time": total_time,
                "messages_count": len(test_messages),
                "avg_time_per_message": total_time / len(test_messages),
                "messages_per_second": len(test_messages) / total_time,
                "successful_responses": len([r for r in responses if not r.startswith("Error:")]),
                "error_count": len([r for r in responses if r.startswith("Error:")])
            }
        
        return {
            "model_name": self.get_model_name(),
            "benchmark_results": results,
            "pool_stats": self.get_pool_stats()
        }

    def __del__(self):
        """析构函数，清理资源"""
        try:
            self.clear_connection_pool()
        except:
            pass
