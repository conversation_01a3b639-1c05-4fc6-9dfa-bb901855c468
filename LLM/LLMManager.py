from typing import Dict

from LLM.BaseLLMHelper import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from LLM.EmbeddingsManager import EmbeddingsManager
from LLM.OllamaLLMHelper import <PERSON><PERSON><PERSON><PERSON><PERSON>Hel<PERSON>
from LLM.AsyncOllamaLLMHelper import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from LLM.OpenAiLLMHelper import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from Models.peewee.OrmModel import KBModel, ModelsPlatformName
from Services.SqlServer.KBModelsService import KBModelsService


class LLMManager:
    _llm_map: Dict[int, BaseLLMHelper] = {}
    _async_llm_map: Dict[int, AsyncOllamaLLMHelper] = {}

    @staticmethod
    def __create_llm_helper(*, id: int = None, model: KBModel = None, async_mode: bool = False) -> BaseLLMHelper | None:
        if id:
            model = KBModel.get_by_id(id)

        if model.platform_type == ModelsPlatformName.OLLAMA.value:
            if async_mode:
                return AsyncOllama<PERSON><PERSON><PERSON>elper(model)
            else:
                return <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(model)
        elif model.platform_type == ModelsPlatformName.OPENAI.value:
            return OpenAiLLMHelper(model)
        elif model.platform_type == ModelsPlatformName.DEEPSEEK.value:
            return OpenAiLLMHelper(model)
        elif model.platform_type == ModelsPlatformName.SPARK.value:
            return OpenAiLLMHelper(model)
        elif model.platform_type == ModelsPlatformName.QWEN.value:
            return OpenAiLLMHelper(model)
        return None

    def get_llm_helper(self, *, id: int = None, model: KBModel = None, async_mode: bool = False) -> BaseLLMHelper | None:
        """
        获取 LLM Helper

        Args:
            id: 模型ID
            model: 模型对象
            async_mode: 是否使用异步模式（仅对 Ollama 有效）
        """
        target_map = self._async_llm_map if async_mode else self._llm_map
        cache_key = id if id else (model.id if model else None)

        if cache_key is None:
            return None

        llm_helper = target_map.get(cache_key, None)
        if llm_helper is None:
            llm_helper = self.__create_llm_helper(id=id, model=model, async_mode=async_mode)
            if llm_helper is not None:
                target_map[cache_key] = llm_helper
        return llm_helper

    def get_chat_use_llm_helper(self, async_mode: bool = False) -> BaseLLMHelper | None:
        """获取默认聊天模型的 LLM Helper"""
        return self.get_llm_helper(model=KBModelsService.get_default_chat_model(), async_mode=async_mode)

    def get_generate_use_llm_helper(self, async_mode: bool = False) -> BaseLLMHelper | None:
        """获取默认生成模型的 LLM Helper"""
        return self.get_llm_helper(model=KBModelsService.get_generate_model(), async_mode=async_mode)

    def get_async_ollama_helper(self, *, id: int = None, model: KBModel = None) -> AsyncOllamaLLMHelper | None:
        """
        专门获取异步 Ollama Helper

        Args:
            id: 模型ID
            model: 模型对象

        Returns:
            AsyncOllamaLLMHelper 实例或 None
        """
        helper = self.get_llm_helper(id=id, model=model, async_mode=True)
        return helper if isinstance(helper, AsyncOllamaLLMHelper) else None

    def clear_connection_pools(self):
        """清空所有连接池"""
        for helper in self._llm_map.values():
            if hasattr(helper, 'clear_connection_pool'):
                helper.clear_connection_pool()

        for helper in self._async_llm_map.values():
            if hasattr(helper, 'clear_connection_pool'):
                helper.clear_connection_pool()

    def get_all_pool_stats(self) -> dict:
        """获取所有连接池的统计信息"""
        stats = {
            "sync_helpers": {},
            "async_helpers": {}
        }

        for key, helper in self._llm_map.items():
            if hasattr(helper, 'get_pool_stats'):
                stats["sync_helpers"][key] = helper.get_pool_stats()

        for key, helper in self._async_llm_map.items():
            if hasattr(helper, 'get_pool_stats'):
                stats["async_helpers"][key] = helper.get_pool_stats()

        return stats


sys_llm_manager = LLMManager()
embeddings_manager = EmbeddingsManager()
