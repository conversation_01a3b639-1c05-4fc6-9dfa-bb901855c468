from typing import Optional
from pydantic import BaseModel, Field

# 百度联网搜索的对象
class BaiduSearchItem(BaseModel):
    content: Optional[str] = Field(default=None)
    date: Optional[str] = Field(default=None)
    icon: Optional[str] = Field(default=None)
    id: Optional[int] = Field(default=None)
    image: Optional[str] = Field(default=None)
    title: Optional[str] = Field(default=None)
    type: Optional[str] = Field(default="web")
    url: Optional[str] = Field(default=None)
    video: Optional[str] = Field(default=None)
    web_anchor: Optional[str] = Field(default="")
