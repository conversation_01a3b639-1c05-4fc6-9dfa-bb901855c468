#!/usr/bin/env python3
"""
Ollama 并发性能对比脚本
对比优化前后的性能差异
"""

import asyncio
import time
import statistics
from typing import List, Dict
from concurrent.futures import ThreadPoolExecutor, as_completed

from LLM.LLMManager import sys_llm_manager
from Utils.logs.LoggingConfig import logger


class PerformanceComparison:
    """性能对比测试类"""
    
    def __init__(self):
        self.test_messages = [
            "什么是人工智能？",
            "Python 有哪些优势？",
            "如何学习机器学习？",
            "什么是深度学习？",
            "解释一下神经网络",
            "什么是自然语言处理？",
            "机器学习有哪些应用？",
            "什么是计算机视觉？",
            "解释一下强化学习",
            "什么是大语言模型？"
        ]
    
    def test_original_sequential(self) -> Dict:
        """测试原始顺序执行方式"""
        print("🔄 测试原始顺序执行...")
        
        # 获取传统的 LLM Helper
        helper = sys_llm_manager.get_chat_use_llm_helper()
        if helper is None:
            return {"error": "未找到 LLM Helper"}
        
        start_time = time.time()
        responses = []
        response_times = []
        
        for i, message in enumerate(self.test_messages):
            msg_start = time.time()
            try:
                llm = helper.get_llm_chat_object(0.7)
                # 模拟同步调用
                response = f"Response to: {message}"  # 实际应该调用 llm.invoke()
                responses.append(response)
                
                msg_time = time.time() - msg_start
                response_times.append(msg_time)
                
                print(f"  消息 {i+1}/{len(self.test_messages)} 完成 ({msg_time:.2f}s)")
                
            except Exception as e:
                logger.error(f"消息 {i+1} 处理失败: {e}")
                responses.append(f"Error: {e}")
                response_times.append(0)
        
        total_time = time.time() - start_time
        
        return {
            "method": "原始顺序执行",
            "total_time": total_time,
            "message_count": len(self.test_messages),
            "avg_time_per_message": statistics.mean(response_times) if response_times else 0,
            "min_time": min(response_times) if response_times else 0,
            "max_time": max(response_times) if response_times else 0,
            "messages_per_second": len(self.test_messages) / total_time if total_time > 0 else 0,
            "success_count": len([r for r in responses if not r.startswith("Error:")]),
            "error_count": len([r for r in responses if r.startswith("Error:")])
        }
    
    def test_thread_pool_concurrent(self, max_workers: int = 4) -> Dict:
        """测试线程池并发执行"""
        print(f"⚡ 测试线程池并发执行 (workers={max_workers})...")
        
        helper = sys_llm_manager.get_chat_use_llm_helper()
        if helper is None:
            return {"error": "未找到 LLM Helper"}
        
        def process_message(message: str) -> tuple:
            """处理单个消息"""
            start = time.time()
            try:
                llm = helper.get_llm_chat_object(0.7)
                # 模拟调用
                response = f"Response to: {message}"
                return response, time.time() - start
            except Exception as e:
                return f"Error: {e}", time.time() - start
        
        start_time = time.time()
        responses = []
        response_times = []
        
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交所有任务
            future_to_message = {
                executor.submit(process_message, msg): msg 
                for msg in self.test_messages
            }
            
            # 收集结果
            completed = 0
            for future in as_completed(future_to_message):
                response, msg_time = future.result()
                responses.append(response)
                response_times.append(msg_time)
                
                completed += 1
                print(f"  任务 {completed}/{len(self.test_messages)} 完成 ({msg_time:.2f}s)")
        
        total_time = time.time() - start_time
        
        return {
            "method": f"线程池并发 (workers={max_workers})",
            "total_time": total_time,
            "message_count": len(self.test_messages),
            "avg_time_per_message": statistics.mean(response_times) if response_times else 0,
            "min_time": min(response_times) if response_times else 0,
            "max_time": max(response_times) if response_times else 0,
            "messages_per_second": len(self.test_messages) / total_time if total_time > 0 else 0,
            "success_count": len([r for r in responses if not r.startswith("Error:")]),
            "error_count": len([r for r in responses if r.startswith("Error:")])
        }
    
    async def test_async_concurrent(self, max_concurrent: int = 4) -> Dict:
        """测试异步并发执行"""
        print(f"🚀 测试异步并发执行 (concurrent={max_concurrent})...")
        
        async_helper = sys_llm_manager.get_async_ollama_helper()
        if async_helper is None:
            return {"error": "未找到异步 Ollama Helper"}
        
        start_time = time.time()
        
        try:
            # 使用优化后的批量处理
            responses = await async_helper.batch_chat_async(
                self.test_messages,
                max_concurrent=max_concurrent,
                temperature=0.7
            )
            
            total_time = time.time() - start_time
            
            return {
                "method": f"异步并发 (concurrent={max_concurrent})",
                "total_time": total_time,
                "message_count": len(self.test_messages),
                "avg_time_per_message": total_time / len(self.test_messages),
                "messages_per_second": len(self.test_messages) / total_time if total_time > 0 else 0,
                "success_count": len([r for r in responses if not r.startswith("Error:")]),
                "error_count": len([r for r in responses if r.startswith("Error:")])
            }
            
        except Exception as e:
            total_time = time.time() - start_time
            logger.error(f"异步并发测试失败: {e}")
            return {
                "method": f"异步并发 (concurrent={max_concurrent})",
                "total_time": total_time,
                "error": str(e)
            }
    
    def print_results(self, results: List[Dict]):
        """打印测试结果"""
        print("\n" + "="*80)
        print("📊 性能对比结果")
        print("="*80)
        
        # 表头
        print(f"{'方法':<25} {'总时间(s)':<12} {'消息数':<8} {'平均时间(s)':<12} {'吞吐量(msg/s)':<15} {'成功率':<10}")
        print("-" * 80)
        
        for result in results:
            if "error" in result:
                print(f"{result['method']:<25} {'ERROR':<12} {'-':<8} {'-':<12} {'-':<15} {'-':<10}")
                continue
            
            method = result['method']
            total_time = f"{result['total_time']:.2f}"
            message_count = str(result['message_count'])
            avg_time = f"{result.get('avg_time_per_message', 0):.2f}"
            throughput = f"{result['messages_per_second']:.2f}"
            success_rate = f"{result['success_count']}/{result['message_count']}"
            
            print(f"{method:<25} {total_time:<12} {message_count:<8} {avg_time:<12} {throughput:<15} {success_rate:<10}")
        
        print("-" * 80)
        
        # 性能提升分析
        if len(results) >= 2:
            baseline = results[0]
            if "error" not in baseline:
                print("\n📈 性能提升分析:")
                baseline_throughput = baseline['messages_per_second']
                
                for result in results[1:]:
                    if "error" not in result:
                        improvement = (result['messages_per_second'] / baseline_throughput - 1) * 100
                        print(f"  {result['method']}: {improvement:+.1f}% 吞吐量提升")
    
    async def run_comparison(self):
        """运行完整的性能对比"""
        print("🎯 开始 Ollama 并发性能对比测试")
        print(f"测试消息数量: {len(self.test_messages)}")
        print("-" * 50)
        
        results = []
        
        # 1. 原始顺序执行
        try:
            result1 = self.test_original_sequential()
            results.append(result1)
        except Exception as e:
            logger.error(f"顺序执行测试失败: {e}")
            results.append({"method": "原始顺序执行", "error": str(e)})
        
        # 2. 线程池并发 (不同worker数)
        for workers in [2, 4, 8]:
            try:
                result = self.test_thread_pool_concurrent(workers)
                results.append(result)
            except Exception as e:
                logger.error(f"线程池并发测试失败 (workers={workers}): {e}")
                results.append({"method": f"线程池并发 (workers={workers})", "error": str(e)})
        
        # 3. 异步并发 (不同并发数)
        for concurrent in [2, 4, 8]:
            try:
                result = await self.test_async_concurrent(concurrent)
                results.append(result)
            except Exception as e:
                logger.error(f"异步并发测试失败 (concurrent={concurrent}): {e}")
                results.append({"method": f"异步并发 (concurrent={concurrent})", "error": str(e)})
        
        # 打印结果
        self.print_results(results)
        
        # 连接池统计
        print("\n🔍 连接池统计:")
        try:
            all_stats = sys_llm_manager.get_all_pool_stats()
            for category, stats in all_stats.items():
                if stats:
                    print(f"  {category}: {len(stats)} 个实例")
                    for key, stat in stats.items():
                        print(f"    模型 {key}: {stat}")
        except Exception as e:
            print(f"  获取统计失败: {e}")
        
        print("\n✅ 性能对比测试完成!")


async def main():
    """主函数"""
    comparison = PerformanceComparison()
    await comparison.run_comparison()
    
    # 清理资源
    sys_llm_manager.clear_connection_pools()
    print("🧹 资源清理完成")


if __name__ == "__main__":
    asyncio.run(main())
