import asyncio
import os
from contextlib import asynccontextmanager

import uvicorn
from fastapi import FastAPI
from starlette.middleware.cors import CORSMiddleware

from Configs.Config import SysConfig
from Controller.KBAnalysisTaskController import router as kb_analysis_task_router
from Controller.KBConversationController import router as kb_conversation_router
from Controller.KBConversationDetailController import router as kb_conversation_detail_router
from Controller.KBFileInfoController import router as kb_file_info_router
from Controller.KBFileTrunkInfoController import router as file_trunk_router
from Controller.KnowledgeController import router as kb_router
from Controller.TalentInfoController import router as talent_router
from Controller.InterviewQuestionsController import router as interview_questions_router
from Controller.LLMSettingsController import router as llm_setting_router
from Controller.InterviewController import router as llm_interview
from Models.peewee.OrmModel import create_tables
from Services.ConversationMemory import conversation_memory_manager
from Services.ResourceService import ResourceService
from Services.SqlServer.KBFileInfoService import KBFileInfoService
from Services.TaskServer.KBFileVectorTaskWorker import KBFileVectorTaskWorker
from Services.TaskServer.TaskFactory import TaskFactory
from Utils.AsyncConfig import async_config
from Utils.DBTools.MySQLInitializer import MySQLHandler
from Utils.NacosRegister import NacosRegister
from Utils.logs.LoggingConfig import logger
from mysql.connector.locales.eng import client_error

# from Controller.HrInterviewController import router as hr_interview_router

os.environ['KMP_DUPLICATE_LIB_OK'] = 'True'  # 临时解决方案

file_service = KBFileVectorTaskWorker()
# 创建SQLite数据库实例
threading_db = MySQLHandler()
# 创建Nacos注册器实例
if SysConfig["nacos"].get("enabled", False):
    nacos_register = NacosRegister()


def init_task():
    # 创建表
    create_tables()
    # 刚启动时，解决问题，未处理完，程序停止，下次启动不会继续进行，
    # 将进行中的文件设置成未处理，删除未处理完块
    TaskFactory.clean_task()
    # 初始化法律资源库
    ResourceService.init_resource()


@asynccontextmanager
async def lifespan(app: FastAPI):
    logger.info("Application startup...")

    # 根据配置决定是否启动 Nacos
    if SysConfig["nacos"].get("enabled", False):
        nacos_register.register_service()

    # 启动文件处理服务
    if SysConfig["vector_gen"].get("enabled", False):
        file_service.start()
    # 启动数据库连接
    try:
        threading_db.connect()
        logger.info("mysql连接成功！(*^_^*)~~~~")
    except Exception as e:
        logger.error(f"mysql连接失败,异常信息如下: {e}", exc_info=True)

    # 初始化
    try:
        init_task()
    except Exception as e:
        logger.error(f"初始化出现异常: {e}", exc_info=True)
    # 启动sqlite数据库连接
    try:
        conversation_memory_manager.start()
        # threading_sqlite_db.connect()
        logger.info("sqlite连接成功！(*^_^*)~~~~")
    except Exception as e:
        logger.error(f"sqlite连接失败,异常信息如下: {e}", exc_info=True)

    # 初始化异步配置
    try:
        # async_config已经在导入时初始化，这里只是确保配置正确
        logger.info("异步配置初始化成功！")
    except Exception as e:
        logger.error(f"异步配置初始化失败: {e}", exc_info=True)

    yield

    logger.info("Application shutdown...")
    # 关闭时执行
    if SysConfig["vector_gen"].get("enabled", False):
        file_service.stop()
        logger.info("File processing service stopped")
    if SysConfig["nacos"].get("enabled", False):
        nacos_register.deregister_service()
        logger.info("Nacos服务已注销！(*^_^*)~~~~")
    # 关闭数据库连接
    if SysConfig["mysql"].get("enabled", False):
        threading_db.close()
        logger.info("mysql连接已关闭！(*^_^*)~~~~")
    # 关闭数据库连接
    if SysConfig["sqlite"].get("enabled", False):
        # threading_sqlite_db.close()
        conversation_memory_manager.stop()
        logger.info("sqlite连接已关闭！(*^_^*)~~~~")

    # 关闭异步配置的线程池
    try:
        async_config.shutdown()
        logger.info("异步配置已关闭！(*^_^*)~~~~")
    except Exception as e:
        logger.error(f"异步配置关闭失败: {e}", exc_info=True)


app = FastAPI(lifespan=lifespan,docs_url=None,redoc_url=None,openapi_url=None)

cross_domain = bool(SysConfig.get("cross_domain", False))
if cross_domain:
    # 添加CORS中间件配置（放在其他中间件之前）
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],  # 允许所有来源
        allow_credentials=True,
        allow_methods=["*"],  # 允许所有方法
        allow_headers=["*"],  # 允许所有头
    )

# 添加路由
app.include_router(kb_router)
app.include_router(kb_file_info_router)
app.include_router(kb_conversation_router)
app.include_router(kb_conversation_detail_router)
app.include_router(file_trunk_router)
app.include_router(kb_analysis_task_router)
app.include_router(talent_router)
app.include_router(llm_setting_router)
app.include_router(llm_interview)
app.include_router(interview_questions_router)


# app.include_router(hr_interview_router)


async def shutdown_handler():
    """服务器关闭时的处理函数"""
    logger.info("Shutting down server...")

    # 按顺序停止各个服务
    try:
        # 停止sql处理服务
        threading_db.close()
        logger.info("mysql连接已关闭！(*^_^*)~~~~")
        file_service.stop()
        logger.info("File processing service stopped")
        # 等待服务完全停止
        await asyncio.sleep(1)

        logger.info("Server shutdown complete")
    except Exception as e:
        logger.error(f"Error during shutdown: {e}")


# 注册关闭处理函数
app.add_event_handler("shutdown", shutdown_handler)

if __name__ == "__main__":
    logger.info("Starting server...")

    # 获取并发配置
    server_config = SysConfig.get("server", {})
    workers = server_config.get("workers", 1) # 根据CPU核心数设置工作进程数
    limit_concurrency = server_config.get("limit_concurrency", 100)
    limit_max_requests = server_config.get("limit_max_requests", 1000)

    if workers > 1:
        # 多进程模式（生产环境）
        logger.info(f"启动多进程模式，工作进程数: {workers}")
        uvicorn.run(
            "Server:app",
            host="0.0.0.0",
            port=8000,
            workers=workers,
            log_level="info"
        )
    else:
        # 单进程模式（开发环境），优化并发参数
        logger.info(f"启动单进程模式，并发限制: {limit_concurrency}, 最大请求数: {limit_max_requests}")
        uvicorn.run(
            app,
            host="0.0.0.0",
            port=8000,
            log_level="info",
            limit_concurrency=limit_concurrency,
            limit_max_requests=limit_max_requests,
            loop="asyncio",
            access_log=False  # 减少日志开销
        )
