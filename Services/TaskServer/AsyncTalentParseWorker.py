import asyncio
import aiofiles
from aiofiles import os
import fitz  # PyMuPDF
import re
from Configs.Config import SysConfig
from Services.ContentLoader.LoaderFactory import LoaderFactory
from Services.TaskServer.BaseGenTask import BaseGenTask
from Utils.AsyncConfig import async_config
from Utils.FileDownloader import FileDownloader
from Utils.logs.LoggingConfig import logger


class AsyncTalentParseWorker(BaseGenTask):
    """异步版本的简历解析器，避免阻塞主线程"""
    
    def __init__(self, fileUrl: str):
        super().__init__(fileUrl)
        # 服务实例
        self.temp_dir = SysConfig["talent"]["file_dir"]
        self.file_downloader = FileDownloader(self.temp_dir)

    async def _task(self):
        """异步执行文件解析任务"""
        try:
            # 异步下载文件
            local_file_url = await self._download_file_async()
            
            # 基础校验
            if not await aiofiles.os.path.exists(local_file_url):
                raise FileNotFoundError(f"文件不存在: {local_file_url}")
            
            # 异步解析文件内容
            content = await self._parse_file_async(local_file_url)
            
            # 清理临时文件
            await self._cleanup_file_async(local_file_url)
            
            return content
            
        except Exception as e:
            logger.error(f"文件解析失败: {str(e)}")
            raise e

    async def _download_file_async(self) -> str:
        """异步下载文件"""
        return await async_config.run_in_download_executor(
            self.file_downloader.download,
            self._file_url
        )

    async def _parse_file_async(self, local_file_url: str) -> str:
        """异步解析文件内容"""
        # 获取文件后缀
        file_extension = local_file_url.split('.')[-1].lower()
        
        if file_extension == 'pdf':
            # PDF文件使用专门的解析方法
            return await self._parse_pdf_async(local_file_url)
        else:
            # 其他文件类型使用LoaderFactory
            return await self._parse_document_async(local_file_url)

    async def _parse_pdf_async(self, file_path: str) -> str:
        """异步解析PDF文件"""
        return await async_config.run_in_file_executor(
            self._sync_parse_pdf,
            file_path
        )

    def _sync_parse_pdf(self, file_path: str) -> str:
        """同步解析PDF文件（在线程池中执行）"""
        try:
            # 打开PDF文件
            doc = fitz.open(file_path)
            
            # 遍历每一页
            text = ""
            for page_num in range(len(doc)):
                page = doc.load_page(page_num)
                text += page.get_text("text")
            
            doc.close()
            return self._clean_text_final(text)
            
        except Exception as e:
            logger.error(f"PDF解析失败: {str(e)}")
            raise e

    async def _parse_document_async(self, local_file_url: str) -> str:
        """异步解析文档文件"""
        return await async_config.run_in_file_executor(
            self._sync_parse_document,
            local_file_url
        )

    def _sync_parse_document(self, local_file_url: str) -> str:
        """同步解析文档文件（在线程池中执行）"""
        try:
            # 初始化文件加载器
            loader = LoaderFactory.get_file_loader(local_file_url, 400)
            if not loader:
                raise ValueError(f"不支持的文件类型: {local_file_url}")
            
            # 开始任务
            docs = loader.load(file_name="")
            if not docs:
                raise ValueError(f"文件加载失败: {local_file_url}")
            
            content = ""
            for doc in docs:
                content += doc.page_content
            
            return content
            
        except Exception as e:
            logger.error(f"文档解析失败: {str(e)}")
            raise e

    async def _cleanup_file_async(self, local_file_url: str):
        """异步清理临时文件"""
        await async_config.run_in_file_executor(
            self.file_downloader.remove,
            local_file_url
        )

    def _clean_text_final(self, text: str) -> str:
        """清理文本内容"""
        # 替换特殊符号
        text = re.sub(r'[●•→/|◆■©]+', ' ', text)
        # 合并空格和换行
        text = re.sub(r'\s+', ' ', text)
        # 去除首尾空白
        text = text.strip()
        # 去重行
        lines = [line.strip() for line in text.split('\n') if len(line.strip()) > 3]
        return '\n'.join(lines)

    def _update_task_state(self, *args, **kwargs):
        """
        实现抽象方法 _update_task_state。
        这里可以根据实际业务需求添加具体逻辑，
        目前暂时不做任何操作。
        """
        pass
