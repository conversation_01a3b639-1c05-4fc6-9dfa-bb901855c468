"""
并发 Ollama 功能测试
测试优化后的 OllamaLLMHelper 并发性能和稳定性
"""

import asyncio
import pytest
import time
from unittest.mock import Mock, patch
from typing import List

from LLM.OllamaLLMHelper import <PERSON>llamaLLMHelper, ConcurrentOllamaClient
from LLM.AsyncOllamaLLMHelper import As<PERSON><PERSON><PERSON><PERSON>LLMHelper
from LLM.LLMManager import LLMManager
from Models.peewee.OrmModel import KBModel


class TestConcurrentOllamaClient:
    """测试并发 Ollama 客户端"""
    
    def setup_method(self):
        """测试前准备"""
        # 创建模拟的 KBModel
        self.mock_model = Mock(spec=KBModel)
        self.mock_model.api_url = "http://localhost:11434"
        self.mock_model.model_name = "llama3.1:8b"
        self.mock_model.model_context = 4096
    
    def test_singleton_pattern(self):
        """测试单例模式"""
        client1 = ConcurrentOllamaClient("http://localhost:11434", "llama3.1:8b")
        client2 = ConcurrentOllamaClient("http://localhost:11434", "llama3.1:8b")
        
        # 应该是同一个实例
        assert client1 is client2
        
        # 不同配置应该是不同实例
        client3 = ConcurrentOllamaClient("http://localhost:11434", "qwen2.5:7b")
        assert client1 is not client3
    
    def test_connection_pool_management(self):
        """测试连接池管理"""
        client = ConcurrentOllamaClient("http://localhost:11434", "llama3.1:8b")
        
        # 测试获取实例
        chat_instance1 = client.get_chat_instance(0.7, 4096)
        chat_instance2 = client.get_chat_instance(0.7, 4096)
        
        # 相同配置应该返回相同实例
        assert chat_instance1 is chat_instance2
        
        # 不同配置应该返回不同实例
        chat_instance3 = client.get_chat_instance(0.5, 4096)
        assert chat_instance1 is not chat_instance3
    
    def test_pool_size_limit(self):
        """测试连接池大小限制"""
        client = ConcurrentOllamaClient("http://localhost:11434", "llama3.1:8b")
        client._max_pool_size = 3  # 设置小的池大小用于测试
        
        # 创建多个不同配置的实例
        instances = []
        for i in range(5):
            instance = client.get_chat_instance(0.1 + i * 0.1, 4096)
            instances.append(instance)
        
        # 池大小应该被限制
        assert len(client._chat_pool) <= client._max_pool_size
    
    def test_clear_pool(self):
        """测试清空连接池"""
        client = ConcurrentOllamaClient("http://localhost:11434", "llama3.1:8b")
        
        # 创建一些实例
        client.get_chat_instance(0.7, 4096)
        client.get_llm_instance(0.5, 2048)
        
        assert len(client._chat_pool) > 0 or len(client._llm_pool) > 0
        
        # 清空池
        client.clear_pool()
        
        assert len(client._chat_pool) == 0
        assert len(client._llm_pool) == 0


class TestOllamaLLMHelper:
    """测试优化后的 OllamaLLMHelper"""
    
    def setup_method(self):
        """测试前准备"""
        self.mock_model = Mock(spec=KBModel)
        self.mock_model.api_url = "http://localhost:11434"
        self.mock_model.model_name = "llama3.1:8b"
        self.mock_model.model_context = 4096
        self.mock_model.id = 1
    
    def test_helper_initialization(self):
        """测试 Helper 初始化"""
        helper = OllamaLLMHelper(self.mock_model)
        
        assert helper._model == self.mock_model
        assert helper._concurrent_client is not None
        assert helper._concurrent_client.base_url == self.mock_model.api_url
        assert helper._concurrent_client.model_name == self.mock_model.model_name
    
    def test_get_llm_objects(self):
        """测试获取 LLM 对象"""
        helper = OllamaLLMHelper(self.mock_model)
        
        # 测试获取 LLM 对象
        llm = helper.get_llm_object(0.7)
        assert llm is not None
        
        # 测试获取 Chat LLM 对象
        chat_llm = helper.get_llm_chat_object(0.7)
        assert chat_llm is not None
        
        # 测试相同参数返回相同实例
        llm2 = helper.get_llm_object(0.7)
        assert llm is llm2
    
    def test_pool_stats(self):
        """测试连接池统计"""
        helper = OllamaLLMHelper(self.mock_model)
        
        # 创建一些连接
        helper.get_llm_object(0.7)
        helper.get_llm_chat_object(0.5)
        
        stats = helper.get_pool_stats()
        
        assert "model_name" in stats
        assert "base_url" in stats
        assert "chat_pool_size" in stats
        assert "llm_pool_size" in stats
        assert stats["model_name"] == self.mock_model.model_name


class TestAsyncOllamaLLMHelper:
    """测试异步 OllamaLLMHelper"""
    
    def setup_method(self):
        """测试前准备"""
        self.mock_model = Mock(spec=KBModel)
        self.mock_model.api_url = "http://localhost:11434"
        self.mock_model.model_name = "llama3.1:8b"
        self.mock_model.model_context = 4096
        self.mock_model.id = 1
    
    @pytest.mark.asyncio
    async def test_async_chat(self):
        """测试异步聊天"""
        helper = AsyncOllamaLLMHelper(self.mock_model)
        
        # 模拟 LLM 响应
        with patch.object(helper, 'get_llm_chat_object') as mock_get_llm:
            mock_llm = Mock()
            mock_llm.invoke.return_value = Mock(content="Hello, I'm an AI assistant.")
            mock_get_llm.return_value = mock_llm
            
            response = await helper.chat_async("Hello")
            
            assert response == "Hello, I'm an AI assistant."
            mock_get_llm.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_batch_chat(self):
        """测试批量聊天"""
        helper = AsyncOllamaLLMHelper(self.mock_model)
        
        messages = ["Hello", "How are you?", "What's your name?"]
        
        # 模拟 chat_async 方法
        async def mock_chat_async(msg, **kwargs):
            return f"Response to: {msg}"
        
        with patch.object(helper, 'chat_async', side_effect=mock_chat_async):
            responses = await helper.batch_chat_async(messages, max_concurrent=2)
            
            assert len(responses) == len(messages)
            for i, response in enumerate(responses):
                assert response == f"Response to: {messages[i]}"
    
    @pytest.mark.asyncio
    async def test_health_check(self):
        """测试健康检查"""
        helper = AsyncOllamaLLMHelper(self.mock_model)
        
        # 模拟成功的聊天响应
        with patch.object(helper, 'chat_async', return_value="I'm healthy!"):
            health_status = await helper.health_check_async()
            
            assert health_status["status"] == "healthy"
            assert "response_time" in health_status
            assert "model_name" in health_status
            assert "pool_stats" in health_status
    
    @pytest.mark.asyncio
    async def test_health_check_failure(self):
        """测试健康检查失败情况"""
        helper = AsyncOllamaLLMHelper(self.mock_model)
        
        # 模拟聊天失败
        with patch.object(helper, 'chat_async', side_effect=Exception("Connection failed")):
            health_status = await helper.health_check_async()
            
            assert health_status["status"] == "unhealthy"
            assert "error" in health_status
            assert "Connection failed" in health_status["error"]
    
    @pytest.mark.asyncio
    async def test_benchmark(self):
        """测试性能基准测试"""
        helper = AsyncOllamaLLMHelper(self.mock_model)
        
        test_messages = ["Test 1", "Test 2", "Test 3"]
        
        # 模拟批量聊天
        async def mock_batch_chat(messages, **kwargs):
            return [f"Response to {msg}" for msg in messages]
        
        with patch.object(helper, 'batch_chat_async', side_effect=mock_batch_chat):
            results = await helper.benchmark_async(test_messages, concurrent_levels=[1, 2])
            
            assert "benchmark_results" in results
            assert "concurrency_1" in results["benchmark_results"]
            assert "concurrency_2" in results["benchmark_results"]
            
            for level_results in results["benchmark_results"].values():
                assert "total_time" in level_results
                assert "messages_count" in level_results
                assert "avg_time_per_message" in level_results


class TestLLMManagerIntegration:
    """测试 LLMManager 集成"""
    
    def setup_method(self):
        """测试前准备"""
        self.mock_model = Mock(spec=KBModel)
        self.mock_model.api_url = "http://localhost:11434"
        self.mock_model.model_name = "llama3.1:8b"
        self.mock_model.model_context = 4096
        self.mock_model.id = 1
        self.mock_model.platform_type = "ollama"
    
    def test_get_async_helper(self):
        """测试获取异步 Helper"""
        manager = LLMManager()
        
        with patch('LLM.LLMManager.KBModel.get_by_id', return_value=self.mock_model):
            # 获取同步 Helper
            sync_helper = manager.get_llm_helper(id=1, async_mode=False)
            assert isinstance(sync_helper, OllamaLLMHelper)
            assert not isinstance(sync_helper, AsyncOllamaLLMHelper)
            
            # 获取异步 Helper
            async_helper = manager.get_llm_helper(id=1, async_mode=True)
            assert isinstance(async_helper, AsyncOllamaLLMHelper)
    
    def test_pool_stats_collection(self):
        """测试连接池统计收集"""
        manager = LLMManager()
        
        with patch('LLM.LLMManager.KBModel.get_by_id', return_value=self.mock_model):
            # 创建一些 Helper
            sync_helper = manager.get_llm_helper(id=1, async_mode=False)
            async_helper = manager.get_llm_helper(id=1, async_mode=True)
            
            # 获取统计信息
            stats = manager.get_all_pool_stats()
            
            assert "sync_helpers" in stats
            assert "async_helpers" in stats


@pytest.mark.integration
class TestConcurrentPerformance:
    """并发性能集成测试"""
    
    def setup_method(self):
        """测试前准备"""
        self.mock_model = Mock(spec=KBModel)
        self.mock_model.api_url = "http://localhost:11434"
        self.mock_model.model_name = "llama3.1:8b"
        self.mock_model.model_context = 4096
        self.mock_model.id = 1
    
    @pytest.mark.asyncio
    async def test_concurrent_vs_sequential(self):
        """测试并发与顺序执行的性能对比"""
        helper = AsyncOllamaLLMHelper(self.mock_model)
        
        messages = ["Test message"] * 5
        
        # 模拟聊天延迟
        async def mock_chat_with_delay(msg, **kwargs):
            await asyncio.sleep(0.1)  # 模拟100ms延迟
            return f"Response to: {msg}"
        
        with patch.object(helper, 'chat_async', side_effect=mock_chat_with_delay):
            # 测试顺序执行
            start_time = time.time()
            sequential_responses = []
            for msg in messages:
                response = await helper.chat_async(msg)
                sequential_responses.append(response)
            sequential_time = time.time() - start_time
            
            # 测试并发执行
            start_time = time.time()
            concurrent_responses = await helper.batch_chat_async(messages, max_concurrent=5)
            concurrent_time = time.time() - start_time
            
            # 并发应该更快
            assert concurrent_time < sequential_time
            assert len(concurrent_responses) == len(sequential_responses)
    
    @pytest.mark.asyncio
    async def test_connection_pool_reuse(self):
        """测试连接池复用"""
        helper = OllamaLLMHelper(self.mock_model)
        
        # 多次获取相同配置的对象
        llm1 = helper.get_llm_chat_object(0.7, 4096)
        llm2 = helper.get_llm_chat_object(0.7, 4096)
        llm3 = helper.get_llm_chat_object(0.7, 4096)
        
        # 应该是同一个实例（连接复用）
        assert llm1 is llm2 is llm3
        
        # 不同配置应该是不同实例
        llm4 = helper.get_llm_chat_object(0.5, 4096)
        assert llm1 is not llm4


if __name__ == "__main__":
    # 运行测试
    pytest.main([__file__, "-v"])
