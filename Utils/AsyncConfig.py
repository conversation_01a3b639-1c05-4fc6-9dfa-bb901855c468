import asyncio
import threading
from concurrent.futures import ThreadPoolExecutor
from typing import Optional

from Configs.Config import SysConfig
from Utils.logs.LoggingConfig import logger


class AsyncConfig:
    """
    全局异步配置管理器
    统一管理线程池资源，避免重复创建和资源浪费
    """
    _instance: Optional['AsyncConfig'] = None
    _lock = threading.Lock()
    
    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        # 避免重复初始化
        if hasattr(self, '_initialized'):
            return
        
        # 从配置文件读取并发设置，如果没有则使用默认值
        self.max_llm_workers = SysConfig.get("async_config", {}).get("max_llm_workers", 5)
        self.max_file_workers = SysConfig.get("async_config", {}).get("max_file_workers", 3)
        self.max_download_workers = SysConfig.get("async_config", {}).get("max_download_workers", 5)
        
        # 创建全局线程池
        self._llm_executor: Optional[ThreadPoolExecutor] = None
        self._file_executor: Optional[ThreadPoolExecutor] = None
        self._download_executor: Optional[ThreadPoolExecutor] = None
        
        # 创建信号量来限制并发
        self._llm_semaphore = asyncio.Semaphore(self.max_llm_workers)
        self._file_semaphore = asyncio.Semaphore(self.max_file_workers)
        self._download_semaphore = asyncio.Semaphore(self.max_download_workers)
        
        self._initialized = True
        logger.info(f"AsyncConfig initialized: LLM={self.max_llm_workers}, File={self.max_file_workers}, Download={self.max_download_workers}")
    
    @property
    def llm_executor(self) -> ThreadPoolExecutor:
        """获取LLM处理线程池"""
        if self._llm_executor is None:
            self._llm_executor = ThreadPoolExecutor(
                max_workers=self.max_llm_workers,
                thread_name_prefix="LLM-Worker"
            )
        return self._llm_executor
    
    @property
    def file_executor(self) -> ThreadPoolExecutor:
        """获取文件处理线程池"""
        if self._file_executor is None:
            self._file_executor = ThreadPoolExecutor(
                max_workers=self.max_file_workers,
                thread_name_prefix="File-Worker"
            )
        return self._file_executor
    
    @property
    def download_executor(self) -> ThreadPoolExecutor:
        """获取下载线程池"""
        if self._download_executor is None:
            self._download_executor = ThreadPoolExecutor(
                max_workers=self.max_download_workers,
                thread_name_prefix="Download-Worker"
            )
        return self._download_executor
    
    async def run_in_llm_executor(self, func, *args, **kwargs):
        """在LLM线程池中执行函数，带信号量控制"""
        async with self._llm_semaphore:
            loop = asyncio.get_event_loop()
            return await loop.run_in_executor(self.llm_executor, func, *args, **kwargs)
    
    async def run_in_file_executor(self, func, *args, **kwargs):
        """在文件处理线程池中执行函数，带信号量控制"""
        async with self._file_semaphore:
            loop = asyncio.get_event_loop()
            return await loop.run_in_executor(self.file_executor, func, *args, **kwargs)
    
    async def run_in_download_executor(self, func, *args, **kwargs):
        """在下载线程池中执行函数，带信号量控制"""
        async with self._download_semaphore:
            loop = asyncio.get_event_loop()
            return await loop.run_in_executor(self.download_executor, func, *args, **kwargs)
    
    def shutdown(self):
        """关闭所有线程池"""
        executors = [
            ("LLM", self._llm_executor),
            ("File", self._file_executor),
            ("Download", self._download_executor)
        ]
        
        for name, executor in executors:
            if executor is not None:
                logger.info(f"Shutting down {name} executor...")
                executor.shutdown(wait=True)
                logger.info(f"{name} executor shutdown complete")


# 全局实例
async_config = AsyncConfig()
