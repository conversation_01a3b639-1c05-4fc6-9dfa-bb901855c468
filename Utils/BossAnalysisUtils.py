import re
from typing import Dict, List

from Models.agent.TalentInfoProcessing import ContentTrunk
from Utils.logs.LoggingConfig import logger


class ResumeSplitter:
    # 1. 映射：官方字段 -> 所有可能的标题写法（可继续扩充）
    SECTION_KEYWORDS: Dict[str, List[str]] = {
        "workExperience": [
            "工作经历", "工作履历", "职业经历", "工作经验"
        ],
        "projectExperience": [
            "项目经验", "项目经历", "项目背景"
        ],
        "educationExperience": [
            "教育经历", "教育背景", "学历信息"
        ]
    }

    @classmethod
    def _build_pattern(cls) -> Dict[str, re.Pattern]:
        """把关键词列表编译成正则，(?i) 忽略大小写"""
        return {
            field: re.compile(
                "|".join(f"(?:{re.escape(kw)})" for kw in kw_list),
                flags=re.IGNORECASE
            )
            for field, kw_list in cls.SECTION_KEYWORDS.items()
        }

    @classmethod
    def split_resume(cls, text: str) -> ContentTrunk:
        logger.info(f"==== Resume split input ====\n{text}")
        if not text:
            return ContentTrunk()

        # 2. 关键字正则
        patterns = cls._build_pattern()

        # 3. 先找所有命中的位置 [(start, end, field), ...]
        hits: List[tuple] = []
        for field, pattern in patterns.items():
            for m in pattern.finditer(text):
                hits.append((m.start(), m.end(), field))

        # 4. 按出现顺序排序
        hits.sort(key=lambda x: x[0])

        # 5. 分段
        cursor = 0
        sections: Dict[str, str] = {f: "" for f in patterns}
        sections["basicInfo"] = ""

        if not hits:
            # 一个关键字都没命中 -> 全文算 basicInfo
            sections["basicInfo"] = text.strip()
        else:
            # 5.1 第一个关键字之前的内容归入 basicInfo
            first_start = hits[0][0]
            sections["basicInfo"] = text[:first_start].strip()

            # 5.2 中间段落
            for i, (start, end, field) in enumerate(hits):
                next_start = hits[i + 1][0] if i + 1 < len(hits) else len(text)
                content = text[end:next_start].strip()
                sections[field] = content

        # 6. 组装结果
        trunk = ContentTrunk()
        trunk.basicInfo = sections["basicInfo"]
        trunk.workExperience = sections["workExperience"]
        trunk.projectExperience = sections["projectExperience"]
        trunk.educationExperience = sections["educationExperience"]

        # 7. 日志
        logger.info("==== Resume split result ====")
        logger.info(f"basicInfo:\n{trunk.basicInfo}", )
        logger.info(f"workExperience:\n{trunk.workExperience}", )
        logger.info(f"projectExperience:\n{trunk.projectExperience}", )
        logger.info(f"educationExperience:\n{trunk.educationExperience}", )

        return trunk