"""
请求取消检测工具
用于检测客户端断开连接并取消相关任务
"""

import asyncio
import functools
from typing import Callable, Any
from fastapi import Request
from Utils.logs.LoggingConfig import logger


class RequestCancellationError(Exception):
    """请求取消异常"""
    pass


def with_cancellation(func: Callable) -> Callable:
    """
    装饰器：为异步函数添加请求取消检测
    当客户端断开连接时，会抛出RequestCancellationError异常
    """
    @functools.wraps(func)
    async def wrapper(*args, **kwargs):
        # 查找Request对象
        request = None
        for arg in args:
            if isinstance(arg, Request):
                request = arg
                break
        
        if not request:
            # 如果没有Request对象，直接执行原函数
            return await func(*args, **kwargs)
        
        # 创建取消检测任务
        async def check_disconnection():
            try:
                while True:
                    if await request.is_disconnected():
                        logger.info("Client disconnected, cancelling task")
                        raise RequestCancellationError("Client disconnected")
                    await asyncio.sleep(0.5)  # 每0.5秒检查一次
            except asyncio.CancelledError:
                pass
        
        # 创建主任务和检测任务
        main_task = asyncio.create_task(func(*args, **kwargs))
        check_task = asyncio.create_task(check_disconnection())
        
        try:
            # 等待任一任务完成
            done, pending = await asyncio.wait(
                [main_task, check_task],
                return_when=asyncio.FIRST_COMPLETED
            )
            
            # 取消未完成的任务
            for task in pending:
                task.cancel()
                try:
                    await task
                except asyncio.CancelledError:
                    pass
            
            # 检查哪个任务完成了
            for task in done:
                if task == main_task:
                    return task.result()
                elif task == check_task:
                    # 检测任务完成意味着客户端断开了连接
                    try:
                        task.result()
                    except RequestCancellationError:
                        logger.info("Request cancelled due to client disconnection")
                        raise
                        
        except Exception as e:
            # 确保所有任务都被取消
            main_task.cancel()
            check_task.cancel()
            raise
    
    return wrapper


class CancellableTask:
    """可取消的任务包装器"""
    
    def __init__(self, request: Request):
        self.request = request
        self._cancelled = False
        self._check_task = None
        
    async def __aenter__(self):
        # 启动断开连接检测
        self._check_task = asyncio.create_task(self._check_disconnection())
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        # 停止检测任务
        if self._check_task:
            self._check_task.cancel()
            try:
                await self._check_task
            except asyncio.CancelledError:
                pass
    
    async def _check_disconnection(self):
        """检测客户端断开连接"""
        try:
            while not self._cancelled:
                if await self.request.is_disconnected():
                    self._cancelled = True
                    logger.info("Client disconnected detected")
                    break
                await asyncio.sleep(0.5)
        except asyncio.CancelledError:
            pass
    
    def is_cancelled(self) -> bool:
        """检查是否已被取消"""
        return self._cancelled
    
    def check_cancellation(self):
        """检查取消状态，如果已取消则抛出异常"""
        if self._cancelled:
            raise RequestCancellationError("Request was cancelled")


async def run_with_cancellation(request: Request, coro, timeout: float = None):
    """
    运行协程并支持请求取消检测
    
    Args:
        request: FastAPI请求对象
        coro: 要执行的协程
        timeout: 超时时间（秒）
    
    Returns:
        协程的结果
        
    Raises:
        RequestCancellationError: 当客户端断开连接时
        asyncio.TimeoutError: 当超时时
    """
    async def check_disconnection():
        while True:
            if await request.is_disconnected():
                raise RequestCancellationError("Client disconnected")
            await asyncio.sleep(0.5)
    
    # 创建任务
    main_task = asyncio.create_task(coro)
    check_task = asyncio.create_task(check_disconnection())
    
    tasks = [main_task, check_task]
    
    try:
        if timeout:
            # 如果有超时，使用wait_for
            done, pending = await asyncio.wait(
                tasks,
                timeout=timeout,
                return_when=asyncio.FIRST_COMPLETED
            )
        else:
            # 没有超时，等待第一个完成
            done, pending = await asyncio.wait(
                tasks,
                return_when=asyncio.FIRST_COMPLETED
            )
        
        # 取消未完成的任务
        for task in pending:
            task.cancel()
            try:
                await task
            except asyncio.CancelledError:
                pass
        
        # 处理结果
        if not done:
            # 超时情况
            raise asyncio.TimeoutError("Operation timed out")
        
        for task in done:
            if task == main_task:
                return task.result()
            elif task == check_task:
                # 检测任务完成意味着客户端断开
                try:
                    task.result()
                except RequestCancellationError:
                    raise
                    
    except Exception:
        # 确保所有任务都被取消
        for task in tasks:
            task.cancel()
        raise
