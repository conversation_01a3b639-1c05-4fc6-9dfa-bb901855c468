"""
任务管理器
用于管理长时间运行的任务，避免阻塞FastAPI事件循环
"""

import asyncio
import uuid
from concurrent.futures import ThreadPoolExecutor, Future
from typing import Dict, Any, Optional
from enum import Enum
import time
import threading

from Utils.logs.LoggingConfig import logger


class TaskStatus(Enum):
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class TaskResult:
    def __init__(self, task_id: str):
        self.task_id = task_id
        self.status = TaskStatus.PENDING
        self.result: Optional[Any] = None
        self.error: Optional[str] = None
        self.created_at = time.time()
        self.completed_at: Optional[float] = None
        self.progress: float = 0.0
        self.future: Optional[Future] = None
        self.cancel_event = threading.Event()  # 用于通知任务取消
        
    def to_dict(self) -> Dict[str, Any]:
        return {
            "task_id": self.task_id,
            "status": self.status.value,
            "result": self.result,
            "error": self.error,
            "created_at": self.created_at,
            "completed_at": self.completed_at,
            "progress": self.progress,
            "duration": (self.completed_at - self.created_at) if self.completed_at else None
        }


class TaskManager:
    """任务管理器，用于管理后台任务"""
    
    def __init__(self, max_workers: int = 5):
        self.max_workers = max_workers
        self.executor = ThreadPoolExecutor(max_workers=max_workers, thread_name_prefix="TaskManager")
        self.tasks: Dict[str, TaskResult] = {}
        self.cleanup_interval = 300  # 5分钟清理一次完成的任务
        self._cleanup_task = None
        
    async def start(self):
        """启动任务管理器"""
        self._cleanup_task = asyncio.create_task(self._cleanup_completed_tasks())
        logger.info(f"TaskManager started with {self.max_workers} workers")
        
    async def stop(self):
        """停止任务管理器"""
        if self._cleanup_task:
            self._cleanup_task.cancel()
        self.executor.shutdown(wait=True)
        logger.info("TaskManager stopped")
        
    def submit_task(self, func, *args, **kwargs) -> str:
        """提交一个任务到后台执行"""
        task_id = str(uuid.uuid4())
        task_result = TaskResult(task_id)
        self.tasks[task_id] = task_result

        # 在线程池中执行任务
        future = self.executor.submit(self._execute_task, task_result, func, *args, **kwargs)
        task_result.future = future

        logger.info(f"Task {task_id} submitted")
        return task_id
        
    def _execute_task(self, task_result: TaskResult, func, *args, **kwargs):
        """在线程池中执行任务"""
        try:
            # 检查是否已被取消
            if task_result.cancel_event.is_set():
                task_result.status = TaskStatus.CANCELLED
                task_result.completed_at = time.time()
                task_result.error = "Task was cancelled before execution"
                logger.info(f"Task {task_result.task_id} was cancelled before execution")
                return

            task_result.status = TaskStatus.RUNNING
            logger.info(f"Task {task_result.task_id} started")

            # 执行实际任务，传递取消事件
            if hasattr(func, '__code__') and 'cancel_event' in func.__code__.co_varnames:
                result = func(*args, cancel_event=task_result.cancel_event, **kwargs)
            else:
                result = func(*args, **kwargs)

            # 再次检查是否被取消
            if task_result.cancel_event.is_set():
                task_result.status = TaskStatus.CANCELLED
                task_result.completed_at = time.time()
                task_result.error = "Task was cancelled during execution"
                logger.info(f"Task {task_result.task_id} was cancelled during execution")
                return

            task_result.result = result
            task_result.status = TaskStatus.COMPLETED
            task_result.completed_at = time.time()
            task_result.progress = 1.0

            logger.info(f"Task {task_result.task_id} completed successfully")

        except Exception as e:
            if task_result.cancel_event.is_set():
                task_result.status = TaskStatus.CANCELLED
                task_result.error = "Task was cancelled"
                logger.info(f"Task {task_result.task_id} was cancelled")
            else:
                task_result.error = str(e)
                task_result.status = TaskStatus.FAILED
                logger.error(f"Task {task_result.task_id} failed: {e}")
            task_result.completed_at = time.time()
            
    def get_task_status(self, task_id: str) -> Optional[Dict[str, Any]]:
        """获取任务状态"""
        task_result = self.tasks.get(task_id)
        if task_result:
            return task_result.to_dict()
        return None
        
    def get_task_result(self, task_id: str) -> Optional[Any]:
        """获取任务结果"""
        task_result = self.tasks.get(task_id)
        if task_result and task_result.status == TaskStatus.COMPLETED:
            return task_result.result
        return None
        
    def is_task_completed(self, task_id: str) -> bool:
        """检查任务是否完成"""
        task_result = self.tasks.get(task_id)
        return task_result and task_result.status in [TaskStatus.COMPLETED, TaskStatus.FAILED]
        
    def cancel_task(self, task_id: str) -> bool:
        """取消任务"""
        task_result = self.tasks.get(task_id)
        if not task_result:
            return False

        # 如果任务已经完成，无法取消
        if task_result.status in [TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELLED]:
            return False

        # 设置取消标志
        task_result.cancel_event.set()

        # 如果任务还在等待执行，尝试取消Future
        if task_result.future and task_result.status == TaskStatus.PENDING:
            cancelled = task_result.future.cancel()
            if cancelled:
                task_result.status = TaskStatus.CANCELLED
                task_result.error = "Task cancelled"
                task_result.completed_at = time.time()
                logger.info(f"Task {task_id} cancelled successfully")
                return True

        # 如果任务正在运行，标记为取消（任务内部需要检查cancel_event）
        if task_result.status == TaskStatus.RUNNING:
            logger.info(f"Task {task_id} cancellation requested (running task will check cancel_event)")
            return True

        return False
        
    async def _cleanup_completed_tasks(self):
        """定期清理已完成的任务"""
        while True:
            try:
                await asyncio.sleep(self.cleanup_interval)
                current_time = time.time()
                
                # 清理超过1小时的已完成任务
                tasks_to_remove = []
                for task_id, task_result in self.tasks.items():
                    if (task_result.status in [TaskStatus.COMPLETED, TaskStatus.FAILED] and 
                        task_result.completed_at and 
                        current_time - task_result.completed_at > 3600):  # 1小时
                        tasks_to_remove.append(task_id)
                
                for task_id in tasks_to_remove:
                    del self.tasks[task_id]
                    
                if tasks_to_remove:
                    logger.info(f"Cleaned up {len(tasks_to_remove)} completed tasks")
                    
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in cleanup task: {e}")


# 创建全局任务管理器实例
task_manager = TaskManager(max_workers=5)
