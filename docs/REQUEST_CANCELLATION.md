# 请求取消功能说明

## 问题描述

当前端用户点击停止按钮或关闭页面时，虽然HTTP请求被断开，但后台的LLM处理仍在继续运行，造成资源浪费。

## 解决方案

### 1. 请求取消检测工具 (`Utils/RequestCancellation.py`)

**核心功能：**
- 检测客户端断开连接
- 自动取消相关的后台任务
- 支持超时控制
- 提供装饰器和上下文管理器

**主要组件：**

#### `run_with_cancellation()` 函数
```python
async def run_with_cancellation(request: Request, coro, timeout: float = None):
    """运行协程并支持请求取消检测"""
```

#### `RequestCancellationError` 异常
当检测到客户端断开连接时抛出的异常。

#### `CancellableTask` 上下文管理器
用于包装长时间运行的任务，支持取消检测。

### 2. 任务管理器增强 (`Utils/TaskManager.py`)

**新增功能：**
- 任务取消支持
- 取消事件传递
- 任务状态：`CANCELLED`

**关键改进：**
```python
# 新增取消状态
class TaskStatus(Enum):
    CANCELLED = "cancelled"

# 任务结果增加取消事件
class TaskResult:
    def __init__(self, task_id: str):
        self.cancel_event = threading.Event()  # 取消事件
        self.future: Optional[Future] = None   # Future对象引用
```

**取消机制：**
1. **等待中的任务**：直接取消Future对象
2. **运行中的任务**：设置取消事件，任务内部检查并响应
3. **已完成的任务**：无法取消

### 3. 控制器更新

所有主要接口都已更新以支持请求取消：

#### `/info` - 聊天接口
```python
@router.post("/info")
async def chat(talent_info: TalentInfo, request: Request):
    try:
        row = await run_with_cancellation(
            request,
            async_talent_agent.chat_for_answer_async(talent_info.question, talent_info.id),
            timeout=30.0
        )
        return AjaxResult.success(row)
    except RequestCancellationError:
        return AjaxResult.error("请求已被取消")
```

#### `/parse` - 简历解析接口
```python
@router.get("/parse")
async def parse(fileUrl: str = None, request: Request = None):
    try:
        result = await run_with_cancellation(
            request,
            parse_resume_background(fileUrl),
            timeout=30.0
        )
        return result
    except RequestCancellationError:
        return AjaxResult.error("请求已被取消")
```

#### `/analyse` - 简历分析接口
```python
@router.get("/analyse")
async def analyse(fileUrl: str = None, taskId: int = None, id: int = None, request: Request = None):
    try:
        result = await run_with_cancellation(
            request,
            analyse_background(fileUrl, taskId, id),
            timeout=60.0  # 分析任务较复杂，给更长时间
        )
        return result
    except RequestCancellationError:
        # 请求被取消，标记失败状态
        if taskId is not None:
            await async_talent_agent.updateFileStatus_async(id, 2)
        return AjaxResult.error("请求已被取消")
```

### 4. 新增取消接口

#### 取消任务接口
```http
DELETE /agentService/api/talent/task/{task_id}
```

**功能：**
- 取消指定的后台任务
- 返回取消结果

**响应：**
```json
{
  "code": 200,
  "data": {
    "message": "任务取消成功"
  }
}
```

## 工作原理

### 1. 客户端断开检测

```python
async def check_disconnection():
    while True:
        if await request.is_disconnected():
            raise RequestCancellationError("Client disconnected")
        await asyncio.sleep(0.5)  # 每0.5秒检查一次
```

### 2. 任务取消流程

1. **检测断开**：定期检查 `request.is_disconnected()`
2. **设置标志**：设置 `cancel_event` 事件
3. **取消Future**：如果任务还在等待，直接取消
4. **通知任务**：运行中的任务检查取消事件并退出
5. **清理资源**：确保所有相关资源被正确释放

### 3. 任务内部取消检查

```python
def sync_parse_resume(file_url: str, cancel_event=None):
    # 检查是否被取消
    if cancel_event and cancel_event.is_set():
        raise Exception("任务已被取消")
    
    # 执行一些工作...
    
    # 再次检查是否被取消
    if cancel_event and cancel_event.is_set():
        raise Exception("任务已被取消")
```

## 使用效果

### 1. 前端体验改善
- ✅ 点击停止按钮立即停止处理
- ✅ 关闭页面不会继续消耗服务器资源
- ✅ 快速响应用户操作

### 2. 服务器资源优化
- ✅ 避免无效的LLM调用
- ✅ 减少CPU和内存占用
- ✅ 提高整体系统效率

### 3. 并发性能提升
- ✅ 释放被占用的线程池资源
- ✅ 更好的资源分配给活跃请求
- ✅ 提高系统吞吐量

## 配置选项

### 超时设置
不同接口根据复杂度设置不同的超时时间：

```python
# 聊天接口：30秒
timeout=30.0

# 简历解析：30秒  
timeout=30.0

# 简历分析：60秒（更复杂）
timeout=60.0
```

### 检查频率
客户端断开检测频率：

```python
await asyncio.sleep(0.5)  # 每0.5秒检查一次
```

## 测试验证

运行测试脚本验证功能：

```bash
python test_request_cancellation.py
```

**测试内容：**
1. 任务管理器的取消功能
2. 直接接口的请求取消
3. 并发请求中的取消
4. 长时间任务的取消

## 注意事项

1. **兼容性**：保持所有原有接口的向后兼容
2. **错误处理**：正确处理取消异常，避免资源泄漏
3. **状态管理**：取消时正确更新任务状态
4. **日志记录**：记录取消事件便于调试

## 前端集成建议

### JavaScript示例

```javascript
// 创建可取消的请求
const controller = new AbortController();

// 发送请求
fetch('/agentService/api/talent/info', {
  method: 'POST',
  body: JSON.stringify({question: '测试', id: 1}),
  signal: controller.signal
})
.then(response => response.json())
.then(data => console.log(data))
.catch(error => {
  if (error.name === 'AbortError') {
    console.log('请求被取消');
  }
});

// 用户点击停止按钮时
stopButton.onclick = () => {
  controller.abort(); // 取消请求
};
```

通过这个完整的请求取消机制，您的系统现在可以：
- 🎯 **智能响应**：检测客户端断开并立即停止处理
- 🚀 **资源优化**：避免浪费服务器资源在无效请求上
- 💪 **用户体验**：提供真正的停止控制功能
- 📈 **性能提升**：释放资源给活跃的请求使用
