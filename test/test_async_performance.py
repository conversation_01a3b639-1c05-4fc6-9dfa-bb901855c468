import asyncio
import aiohttp
import time
import json
from typing import List, Dict


async def test_concurrent_requests():
    """测试并发请求性能"""
    base_url = "http://localhost:8000"
    
    # 测试数据
    test_data = {
        "simple_request": {
            "url": f"{base_url}/agentService/api/talent/info",
            "method": "POST",
            "data": {"question": "测试问题", "id": 1}
        },
        "parse_request": {
            "url": f"{base_url}/agentService/api/talent/parse",
            "method": "GET",
            "params": {"fileUrl": "http://example.com/test.pdf"}
        }
    }
    
    async def make_request(session: aiohttp.ClientSession, name: str, config: Dict) -> Dict:
        """发送单个请求"""
        start_time = time.time()
        try:
            if config["method"] == "POST":
                async with session.post(
                    config["url"],
                    json=config.get("data", {}),
                    timeout=aiohttp.ClientTimeout(total=300)  # 5分钟超时，足够长
                ) as response:
                    result = await response.json()
                    status = response.status
            else:
                async with session.get(
                    config["url"],
                    params=config.get("params", {}),
                    timeout=aiohttp.ClientTimeout(total=300)  # 5分钟超时，足够长
                ) as response:
                    result = await response.json()
                    status = response.status
            
            end_time = time.time()
            return {
                "name": name,
                "status": status,
                "response_time": end_time - start_time,
                "success": True,
                "result": result
            }
        except Exception as e:
            end_time = time.time()
            return {
                "name": name,
                "status": 0,
                "response_time": end_time - start_time,
                "success": False,
                "error": str(e)
            }
    
    async def test_scenario(scenario_name: str, requests: List[Dict]):
        """测试场景"""
        print(f"\n=== {scenario_name} ===")
        
        async with aiohttp.ClientSession() as session:
            # 并发发送所有请求
            tasks = []
            for i, req_config in enumerate(requests):
                task = make_request(session, f"{req_config['name']}_{i+1}", req_config)
                tasks.append(task)
            
            # 等待所有请求完成
            start_time = time.time()
            results = await asyncio.gather(*tasks, return_exceptions=True)
            total_time = time.time() - start_time
            
            # 分析结果
            successful = sum(1 for r in results if isinstance(r, dict) and r.get("success", False))
            failed = len(results) - successful
            avg_response_time = sum(r.get("response_time", 0) for r in results if isinstance(r, dict)) / len(results)
            
            print(f"总请求数: {len(results)}")
            print(f"成功: {successful}, 失败: {failed}")
            print(f"总耗时: {total_time:.2f}秒")
            print(f"平均响应时间: {avg_response_time:.2f}秒")
            
            # 打印详细结果
            for result in results:
                if isinstance(result, dict):
                    status_text = "✓" if result.get("success") else "✗"
                    print(f"  {status_text} {result['name']}: {result['response_time']:.2f}s")
                    if not result.get("success"):
                        print(f"    错误: {result.get('error', 'Unknown error')}")
    
    # 测试场景1：多个简单请求
    simple_requests = [
        {"name": "simple", "url": test_data["simple_request"]["url"], 
         "method": "POST", "data": test_data["simple_request"]["data"]}
        for _ in range(5)
    ]
    
    await test_scenario("多个简单请求并发测试", simple_requests)
    
    # 测试场景2：混合请求（简单请求 + 复杂请求）
    mixed_requests = [
        {"name": "simple", "url": test_data["simple_request"]["url"], 
         "method": "POST", "data": test_data["simple_request"]["data"]},
        {"name": "simple", "url": test_data["simple_request"]["url"], 
         "method": "POST", "data": test_data["simple_request"]["data"]},
        {"name": "parse", "url": test_data["parse_request"]["url"], 
         "method": "GET", "params": test_data["parse_request"]["params"]},
    ]
    
    await test_scenario("混合请求并发测试", mixed_requests)


async def test_server_health():
    """测试服务器健康状态"""
    print("\n=== 服务器健康检查 ===")
    
    async with aiohttp.ClientSession() as session:
        try:
            # 简单的健康检查请求
            async with session.post(
                "http://localhost:8000/agentService/api/talent/info",
                json={"question": "健康检查", "id": 1},
                timeout=aiohttp.ClientTimeout(total=10)
            ) as response:
                if response.status == 200:
                    print("✓ 服务器响应正常")
                    return True
                else:
                    print(f"✗ 服务器响应异常: {response.status}")
                    return False
        except Exception as e:
            print(f"✗ 服务器连接失败: {str(e)}")
            return False


async def main():
    """主测试函数"""
    print("开始异步性能测试...")
    print("注意：请确保服务器已启动在 http://localhost:8000")
    
    # 健康检查
    if not await test_server_health():
        print("服务器不可用，测试终止")
        return
    
    # 并发测试
    await test_concurrent_requests()
    
    print("\n测试完成！")
    print("\n优化效果说明：")
    print("- 如果多个请求能够并发执行，说明异步优化生效")
    print("- 简单请求应该能快速响应，不受复杂请求影响")
    print("- 总耗时应该接近最慢请求的时间，而不是所有请求时间的总和")
    print("- 接口可以正常完成处理，不会被强制超时中断")


if __name__ == "__main__":
    asyncio.run(main())
