"""
基本功能测试脚本
测试异步改进后的基本功能是否正常
"""
import asyncio
import aiohttp
import time


async def test_server_health():
    """测试服务器健康状态"""
    print("测试服务器健康状态...")
    
    try:
        async with aiohttp.ClientSession() as session:
            # 测试一个简单的接口
            async with session.get("http://localhost:8000/docs") as response:
                if response.status == 200:
                    print("✅ 服务器运行正常")
                    return True
                else:
                    print(f"❌ 服务器响应异常: {response.status}")
                    return False
    except Exception as e:
        print(f"❌ 无法连接到服务器: {e}")
        return False


async def test_concurrent_simple_requests():
    """测试简单接口的并发访问"""
    print("\n测试简单接口并发访问...")
    
    async def make_request(session, url):
        start_time = time.time()
        try:
            async with session.get(url) as response:
                end_time = time.time()
                return {
                    "success": True,
                    "status": response.status,
                    "time": end_time - start_time
                }
        except Exception as e:
            end_time = time.time()
            return {
                "success": False,
                "error": str(e),
                "time": end_time - start_time
            }
    
    async with aiohttp.ClientSession() as session:
        # 并发发送10个请求
        tasks = [
            make_request(session, "http://localhost:8000/docs")
            for _ in range(10)
        ]
        
        start_time = time.time()
        results = await asyncio.gather(*tasks)
        end_time = time.time()
        
        success_count = sum(1 for r in results if r["success"])
        avg_time = sum(r["time"] for r in results) / len(results)
        total_time = end_time - start_time
        
        print(f"✅ 并发测试结果: {success_count}/10 成功")
        print(f"✅ 平均响应时间: {avg_time:.3f}秒")
        print(f"✅ 总耗时: {total_time:.3f}秒")
        
        return success_count == 10


async def test_talent_info_endpoint():
    """测试人才信息接口"""
    print("\n测试人才信息接口...")
    
    try:
        async with aiohttp.ClientSession() as session:
            # 测试聊天接口
            payload = {
                "question": "你好，请介绍一下你的功能",
                "id": 1
            }
            
            start_time = time.time()
            async with session.post(
                "http://localhost:8000/agentService/api/talent/info",
                json=payload
            ) as response:
                end_time = time.time()
                
                if response.status == 200:
                    result = await response.json()
                    print(f"✅ 人才信息接口响应正常")
                    print(f"✅ 响应时间: {end_time - start_time:.3f}秒")
                    return True
                else:
                    print(f"❌ 人才信息接口响应异常: {response.status}")
                    return False
                    
    except Exception as e:
        print(f"❌ 人才信息接口测试失败: {e}")
        return False


async def test_async_task_submission():
    """测试异步任务提交接口"""
    print("\n测试异步任务提交接口...")
    
    try:
        async with aiohttp.ClientSession() as session:
            # 测试异步解析接口
            payload = {
                "content": "https://example.com/test-resume.pdf"
            }
            
            start_time = time.time()
            async with session.post(
                "http://localhost:8000/agentService/api/talent/parse-async",
                json=payload
            ) as response:
                end_time = time.time()
                
                if response.status == 200:
                    result = await response.json()
                    if result.get("success", False) and "task_id" in result.get("data", {}):
                        task_id = result["data"]["task_id"]
                        print(f"✅ 异步任务提交成功")
                        print(f"✅ 任务ID: {task_id}")
                        print(f"✅ 提交响应时间: {end_time - start_time:.3f}秒")
                        
                        # 测试任务状态查询
                        await asyncio.sleep(1)  # 等待1秒
                        async with session.get(
                            f"http://localhost:8000/agentService/api/talent/task-status/{task_id}"
                        ) as status_response:
                            if status_response.status == 200:
                                status_result = await status_response.json()
                                print(f"✅ 任务状态查询成功: {status_result.get('data', {}).get('status', 'unknown')}")
                                return True
                            else:
                                print(f"❌ 任务状态查询失败: {status_response.status}")
                                return False
                    else:
                        print(f"❌ 异步任务提交响应格式异常: {result}")
                        return False
                else:
                    print(f"❌ 异步任务提交失败: {response.status}")
                    return False
                    
    except Exception as e:
        print(f"❌ 异步任务测试失败: {e}")
        return False


async def main():
    """主测试函数"""
    print("开始基本功能测试...\n")
    
    # 测试列表
    tests = [
        ("服务器健康检查", test_server_health),
        ("并发简单请求", test_concurrent_simple_requests),
        ("人才信息接口", test_talent_info_endpoint),
        ("异步任务提交", test_async_task_submission),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"{'='*50}")
        print(f"执行测试: {test_name}")
        print(f"{'='*50}")
        
        try:
            result = await test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ 测试 {test_name} 出现异常: {e}")
            results.append((test_name, False))
        
        print()
    
    # 输出测试总结
    print(f"{'='*50}")
    print("测试总结")
    print(f"{'='*50}")
    
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{len(results)} 个测试通过")
    
    if passed == len(results):
        print("\n🎉 所有测试通过！异步改进功能正常。")
    else:
        print(f"\n⚠️  有 {len(results) - passed} 个测试失败，请检查相关功能。")


if __name__ == "__main__":
    asyncio.run(main())
