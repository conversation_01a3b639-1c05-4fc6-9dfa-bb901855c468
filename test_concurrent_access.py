#!/usr/bin/env python3
"""
测试并发访问能力
验证在简历解析过程中，其他接口是否能正常访问
"""

import asyncio
import aiohttp
import time
from typing import List


async def test_simple_endpoint(session: aiohttp.ClientSession, base_url: str) -> dict:
    """测试简单接口（如version）"""
    url = f"{base_url}/agentService/api/conversation/version"
    start_time = time.time()
    
    try:
        async with session.get(url) as response:
            result = await response.json()
            end_time = time.time()
            return {
                "endpoint": "version",
                "success": True,
                "response_time": end_time - start_time,
                "status": response.status,
                "result": result
            }
    except Exception as e:
        end_time = time.time()
        return {
            "endpoint": "version",
            "success": False,
            "response_time": end_time - start_time,
            "error": str(e)
        }


async def test_rating_endpoint(session: aiohttp.ClientSession, base_url: str) -> dict:
    """测试评分接口"""
    url = f"{base_url}/agentService/api/talent/rating"
    start_time = time.time()
    
    try:
        async with session.get(url) as response:
            result = await response.json()
            end_time = time.time()
            return {
                "endpoint": "rating",
                "success": True,
                "response_time": end_time - start_time,
                "status": response.status,
                "result": result
            }
    except Exception as e:
        end_time = time.time()
        return {
            "endpoint": "rating",
            "success": False,
            "response_time": end_time - start_time,
            "error": str(e)
        }


async def test_parse_endpoint(session: aiohttp.ClientSession, base_url: str) -> dict:
    """测试简历解析接口（模拟耗时操作）"""
    url = f"{base_url}/agentService/api/talent/parse"
    params = {"fileUrl": "http://example.com/test.pdf"}  # 使用一个测试URL
    start_time = time.time()
    
    try:
        async with session.get(url, params=params) as response:
            result = await response.json()
            end_time = time.time()
            return {
                "endpoint": "parse",
                "success": True,
                "response_time": end_time - start_time,
                "status": response.status,
                "result": result
            }
    except Exception as e:
        end_time = time.time()
        return {
            "endpoint": "parse",
            "success": False,
            "response_time": end_time - start_time,
            "error": str(e)
        }


async def concurrent_test(base_url: str = "http://localhost:8000"):
    """并发测试"""
    print("开始并发访问测试...")
    print("=" * 60)
    
    async with aiohttp.ClientSession() as session:
        # 启动一个耗时的解析任务
        print("1. 启动简历解析任务（耗时操作）...")
        parse_task = asyncio.create_task(test_parse_endpoint(session, base_url))
        
        # 等待一小段时间，确保解析任务开始
        await asyncio.sleep(0.5)
        
        # 在解析过程中，并发访问其他接口
        print("2. 在解析过程中，并发访问其他接口...")
        
        # 创建多个并发任务
        concurrent_tasks = []
        for i in range(5):
            concurrent_tasks.append(asyncio.create_task(test_simple_endpoint(session, base_url)))
            concurrent_tasks.append(asyncio.create_task(test_rating_endpoint(session, base_url)))
        
        # 等待所有并发任务完成
        start_time = time.time()
        concurrent_results = await asyncio.gather(*concurrent_tasks, return_exceptions=True)
        concurrent_time = time.time() - start_time
        
        # 等待解析任务完成
        parse_result = await parse_task
        
        # 分析结果
        print("\n3. 测试结果分析:")
        print("-" * 40)
        
        print(f"解析任务结果:")
        print(f"  成功: {parse_result['success']}")
        print(f"  响应时间: {parse_result['response_time']:.2f}s")
        if not parse_result['success']:
            print(f"  错误: {parse_result.get('error', 'Unknown')}")
        
        print(f"\n并发任务结果 (总计 {len(concurrent_tasks)} 个任务):")
        print(f"  总耗时: {concurrent_time:.2f}s")
        
        successful_tasks = [r for r in concurrent_results if isinstance(r, dict) and r.get('success', False)]
        failed_tasks = [r for r in concurrent_results if not (isinstance(r, dict) and r.get('success', False))]
        
        print(f"  成功任务: {len(successful_tasks)}/{len(concurrent_tasks)}")
        print(f"  失败任务: {len(failed_tasks)}")
        
        if successful_tasks:
            avg_response_time = sum(r['response_time'] for r in successful_tasks) / len(successful_tasks)
            max_response_time = max(r['response_time'] for r in successful_tasks)
            min_response_time = min(r['response_time'] for r in successful_tasks)
            
            print(f"  平均响应时间: {avg_response_time:.3f}s")
            print(f"  最大响应时间: {max_response_time:.3f}s")
            print(f"  最小响应时间: {min_response_time:.3f}s")
        
        # 判断并发性能
        print(f"\n4. 并发性能评估:")
        if len(successful_tasks) == len(concurrent_tasks):
            if concurrent_time < 2.0:  # 如果所有任务在2秒内完成
                print("✅ 优秀：所有并发任务都成功完成，响应迅速")
            else:
                print("⚠️  一般：所有任务完成但响应较慢")
        else:
            print("❌ 差：部分任务失败，可能存在阻塞问题")
        
        if failed_tasks:
            print("\n失败任务详情:")
            for i, task in enumerate(failed_tasks[:3]):  # 只显示前3个失败任务
                if isinstance(task, dict):
                    print(f"  {i+1}. {task.get('endpoint', 'unknown')}: {task.get('error', 'Unknown error')}")
                else:
                    print(f"  {i+1}. Exception: {str(task)}")


async def main():
    """主函数"""
    print("🚀 FastAPI 并发访问测试")
    print("测试目标：验证在简历解析过程中，其他接口是否能正常访问")
    print()
    
    try:
        await concurrent_test()
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
    
    print("\n" + "=" * 60)
    print("测试完成！")


if __name__ == "__main__":
    asyncio.run(main())
