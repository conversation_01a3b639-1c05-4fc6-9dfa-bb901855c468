#!/usr/bin/env python3
"""
最终优化验证脚本
验证主要的并发优化是否成功
"""

import asyncio
import aiohttp
import time
import sys
import subprocess


async def test_concurrent_access():
    """测试并发访问能力"""
    print("🔄 测试并发访问能力...")
    
    async def single_request(session, request_id):
        try:
            start_time = time.time()
            async with session.get("http://localhost:8000/agentService/api/llmSetting/list") as response:
                end_time = time.time()
                duration = end_time - start_time
                if response.status == 200:
                    print(f"✅ 请求 {request_id}: {duration:.2f}s")
                    return True, duration
                else:
                    print(f"❌ 请求 {request_id}: 状态码 {response.status}")
                    return False, duration
        except Exception as e:
            end_time = time.time()
            duration = end_time - start_time
            print(f"❌ 请求 {request_id}: 异常 {e}")
            return False, duration

    async with aiohttp.ClientSession() as session:
        # 测试20个并发请求
        tasks = []
        for i in range(20):
            tasks.append(single_request(session, i + 1))
        
        start_time = time.time()
        results = await asyncio.gather(*tasks, return_exceptions=True)
        end_time = time.time()
        
        successful = sum(1 for result in results if isinstance(result, tuple) and result[0])
        total_time = end_time - start_time
        
        print(f"📊 并发测试结果: {successful}/20 成功, 总耗时: {total_time:.2f}s")
        return successful >= 16  # 80%成功率


async def test_resume_parse_non_blocking():
    """测试简历解析时其他接口是否被阻塞"""
    print("\n🔄 测试简历解析非阻塞性...")
    
    async def parse_request(session):
        """发起简历解析请求"""
        try:
            # 使用一个无效的URL来模拟解析请求
            url = "http://localhost:8000/agentService/api/talent/parse?fileUrl=http://invalid-url.com/test.pdf"
            async with session.get(url) as response:
                print(f"解析请求响应: {response.status}")
                return response.status
        except Exception as e:
            print(f"解析请求异常（预期）: {e}")
            return None

    async def basic_request(session, request_id):
        """发起基础请求"""
        try:
            start_time = time.time()
            async with session.get("http://localhost:8000/agentService/api/llmSetting/list") as response:
                end_time = time.time()
                duration = end_time - start_time
                if response.status == 200:
                    print(f"✅ 基础请求 {request_id}: {duration:.2f}s (在解析期间)")
                    return True, duration
                else:
                    print(f"❌ 基础请求 {request_id}: 状态码 {response.status}")
                    return False, duration
        except Exception as e:
            end_time = time.time()
            duration = end_time - start_time
            print(f"❌ 基础请求 {request_id}: 异常 {e}")
            return False, duration

    async with aiohttp.ClientSession() as session:
        # 启动解析请求
        parse_task = asyncio.create_task(parse_request(session))
        
        # 等待一点时间让解析请求开始
        await asyncio.sleep(0.5)
        
        # 同时发起多个基础请求
        basic_tasks = []
        for i in range(8):
            basic_tasks.append(basic_request(session, i + 1))
        
        # 等待所有任务完成
        basic_results = await asyncio.gather(*basic_tasks, return_exceptions=True)
        await parse_task
        
        successful = sum(1 for result in basic_results if isinstance(result, tuple) and result[0])
        print(f"📊 非阻塞测试结果: {successful}/8 基础请求成功")
        
        return successful >= 6  # 75%成功率


async def test_knowledge_chat_non_blocking():
    """测试知识库聊天时其他接口是否被阻塞"""
    print("\n🔄 测试知识库聊天非阻塞性...")
    
    async def chat_request(session):
        """发起知识库聊天请求"""
        try:
            data = {
                "conversation_id": "test_conv",
                "question": "测试问题",
                "kb_id": 1,
                "file_ids": []
            }
            async with session.post(
                "http://localhost:8000/agentService/api/knowledge/chat",
                json=data,
                headers={"Content-Type": "application/json"}
            ) as response:
                print(f"聊天请求响应: {response.status}")
                return response.status
        except Exception as e:
            print(f"聊天请求异常: {e}")
            return None

    async def basic_request(session, request_id):
        """发起基础请求"""
        try:
            start_time = time.time()
            async with session.get("http://localhost:8000/agentService/api/llmSetting/list") as response:
                end_time = time.time()
                duration = end_time - start_time
                if response.status == 200:
                    print(f"✅ 基础请求 {request_id}: {duration:.2f}s (在聊天期间)")
                    return True, duration
                else:
                    print(f"❌ 基础请求 {request_id}: 状态码 {response.status}")
                    return False, duration
        except Exception as e:
            end_time = time.time()
            duration = end_time - start_time
            print(f"❌ 基础请求 {request_id}: 异常 {e}")
            return False, duration

    async with aiohttp.ClientSession() as session:
        # 启动聊天请求
        chat_task = asyncio.create_task(chat_request(session))
        
        # 等待一点时间让聊天请求开始
        await asyncio.sleep(0.5)
        
        # 同时发起多个基础请求
        basic_tasks = []
        for i in range(5):
            basic_tasks.append(basic_request(session, i + 1))
        
        # 等待所有任务完成
        basic_results = await asyncio.gather(*basic_tasks, return_exceptions=True)
        await chat_task
        
        successful = sum(1 for result in basic_results if isinstance(result, tuple) and result[0])
        print(f"📊 聊天非阻塞测试结果: {successful}/5 基础请求成功")
        
        return successful >= 4  # 80%成功率


async def run_optimization_tests():
    """运行优化验证测试"""
    print("🚀 开始最终优化验证")
    print("=" * 50)
    
    # 等待服务器启动
    print("⏳ 等待服务器启动...")
    await asyncio.sleep(3)
    
    tests = [
        ("并发访问测试", test_concurrent_access),
        ("简历解析非阻塞测试", test_resume_parse_non_blocking),
        ("知识库聊天非阻塞测试", test_knowledge_chat_non_blocking),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n📋 执行: {test_name}")
        try:
            result = await test_func()
            results.append((test_name, result))
            if result:
                print(f"✅ {test_name}: 通过")
            else:
                print(f"❌ {test_name}: 失败")
        except Exception as e:
            print(f"❌ {test_name}: 异常 - {e}")
            results.append((test_name, False))
    
    # 总结
    print("\n" + "=" * 50)
    print("📊 优化验证结果总结:")
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
    
    print(f"\n🎯 总体结果: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！并发优化完全成功！")
        print("\n✨ 优化效果:")
        print("  • 简历解析不再阻塞其他接口")
        print("  • 知识库聊天不再阻塞其他接口")
        print("  • 支持高并发访问")
        print("  • 响应时间大幅改善")
    elif passed >= total * 0.8:
        print("⚠️  大部分测试通过，并发优化基本成功")
        print("  • 主要阻塞问题已解决")
        print("  • 系统并发能力显著提升")
    else:
        print("❌ 部分测试失败，需要进一步优化")
    
    return passed >= total * 0.8


def start_server():
    """启动服务器"""
    print("🚀 启动优化后的服务器...")
    try:
        process = subprocess.Popen(
            [sys.executable, "Server.py"],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        return process
    except Exception as e:
        print(f"❌ 启动服务器失败: {e}")
        return None


async def main():
    """主函数"""
    server_process = start_server()
    if not server_process:
        print("无法启动服务器")
        return False
    
    try:
        success = await run_optimization_tests()
        return success
    finally:
        print("\n🔄 关闭服务器...")
        server_process.terminate()
        try:
            server_process.wait(timeout=10)
        except subprocess.TimeoutExpired:
            server_process.kill()
            server_process.wait()
        print("✅ 服务器已关闭")


if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        if success:
            print("\n🎊 恭喜！您的接口并发优化已经成功完成！")
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⚠️  测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 测试过程中发生异常: {e}")
        sys.exit(1)
