#!/usr/bin/env python3
"""
简历解析修复测试脚本
"""
import asyncio
import aiohttp
import sys
import traceback


async def test_basic_endpoints():
    """测试基本接口是否正常工作"""
    print("测试基本接口...")
    
    try:
        async with aiohttp.ClientSession() as session:
            # 测试服务器是否启动
            try:
                async with session.get("http://localhost:8000/docs") as response:
                    if response.status == 200:
                        print("✅ 服务器运行正常")
                    else:
                        print(f"❌ 服务器响应异常: {response.status}")
                        return False
            except Exception as e:
                print(f"❌ 无法连接到服务器: {e}")
                return False
            
            # 测试人才信息接口
            try:
                payload = {
                    "question": "你好",
                    "id": 1
                }
                async with session.post(
                    "http://localhost:8000/agentService/api/talent/info",
                    json=payload
                ) as response:
                    if response.status == 200:
                        result = await response.json()
                        print("✅ 人才信息接口正常")
                    else:
                        print(f"❌ 人才信息接口异常: {response.status}")
                        text = await response.text()
                        print(f"响应内容: {text}")
            except Exception as e:
                print(f"❌ 人才信息接口测试失败: {e}")
                traceback.print_exc()
            
            return True
            
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
        traceback.print_exc()
        return False


async def test_parse_endpoints():
    """测试简历解析接口"""
    print("\n测试简历解析接口...")
    
    # 使用一个测试URL（这个URL不存在，但可以测试接口是否正常响应错误）
    test_url = "https://example.com/nonexistent.pdf"
    
    try:
        async with aiohttp.ClientSession() as session:
            # 测试parse接口
            try:
                async with session.get(
                    f"http://localhost:8000/agentService/api/talent/parse?fileUrl={test_url}"
                ) as response:
                    if response.status == 200:
                        result = await response.json()
                        print("✅ parse接口响应正常")
                        print(f"响应: {result}")
                    else:
                        print(f"❌ parse接口响应异常: {response.status}")
                        text = await response.text()
                        print(f"响应内容: {text}")
            except Exception as e:
                print(f"❌ parse接口测试失败: {e}")
                traceback.print_exc()
            
            # 测试parseDto接口
            try:
                async with session.get(
                    f"http://localhost:8000/agentService/api/talent/parseDto?fileUrl={test_url}"
                ) as response:
                    if response.status == 200:
                        result = await response.json()
                        print("✅ parseDto接口响应正常")
                        print(f"响应: {result}")
                    else:
                        print(f"❌ parseDto接口响应异常: {response.status}")
                        text = await response.text()
                        print(f"响应内容: {text}")
            except Exception as e:
                print(f"❌ parseDto接口测试失败: {e}")
                traceback.print_exc()
            
            # 测试异步接口（如果可用）
            try:
                payload = {"content": test_url}
                async with session.post(
                    "http://localhost:8000/agentService/api/talent/parse-async",
                    json=payload
                ) as response:
                    if response.status == 200:
                        result = await response.json()
                        print("✅ parse-async接口响应正常")
                        print(f"响应: {result}")
                    else:
                        print(f"❌ parse-async接口响应异常: {response.status}")
                        text = await response.text()
                        print(f"响应内容: {text}")
            except Exception as e:
                print(f"❌ parse-async接口测试失败: {e}")
                traceback.print_exc()
                
    except Exception as e:
        print(f"❌ 简历解析接口测试过程中出错: {e}")
        traceback.print_exc()


async def test_concurrent_access():
    """测试并发访问"""
    print("\n测试并发访问...")
    
    async def make_request():
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get("http://localhost:8000/docs") as response:
                    return response.status == 200
        except:
            return False
    
    # 并发发送5个请求
    tasks = [make_request() for _ in range(5)]
    results = await asyncio.gather(*tasks)
    
    success_count = sum(results)
    print(f"并发测试结果: {success_count}/5 成功")
    
    if success_count == 5:
        print("✅ 并发访问正常")
    else:
        print("❌ 并发访问存在问题")


def check_imports():
    """检查关键模块是否可以导入"""
    print("检查模块导入...")
    
    try:
        from Agents.TalentAgent import TalentAgent
        print("✅ TalentAgent 导入成功")
    except Exception as e:
        print(f"❌ TalentAgent 导入失败: {e}")
    
    try:
        from Agents.AsyncTalentAgent import async_talent_agent
        print("✅ AsyncTalentAgent 导入成功")
    except Exception as e:
        print(f"⚠️  AsyncTalentAgent 导入失败: {e}")
    
    try:
        from Utils.AsyncConfig import async_config
        print("✅ AsyncConfig 导入成功")
    except Exception as e:
        print(f"⚠️  AsyncConfig 导入失败: {e}")
    
    try:
        from Utils.TaskManager import task_manager
        print("✅ TaskManager 导入成功")
    except Exception as e:
        print(f"⚠️  TaskManager 导入失败: {e}")
    
    try:
        from Services.TaskServer.TalentParseWorker import TalentParseWorker
        print("✅ TalentParseWorker 导入成功")
    except Exception as e:
        print(f"❌ TalentParseWorker 导入失败: {e}")


async def main():
    """主测试函数"""
    print("简历解析修复测试")
    print("=" * 50)
    
    # 检查模块导入
    check_imports()
    
    print("\n" + "=" * 50)
    
    # 测试基本接口
    basic_ok = await test_basic_endpoints()
    
    if basic_ok:
        # 测试简历解析接口
        await test_parse_endpoints()
        
        # 测试并发访问
        await test_concurrent_access()
    
    print("\n" + "=" * 50)
    print("测试完成")
    print("\n说明:")
    print("1. 如果看到 ✅ 表示功能正常")
    print("2. 如果看到 ❌ 表示存在问题需要修复")
    print("3. 如果看到 ⚠️  表示异步功能不可用，但同步功能应该正常")
    print("4. 简历解析接口可能会返回错误，这是正常的（因为测试URL不存在）")
    print("5. 重要的是接口能够正常响应，而不是卡死或超时")


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n测试被用户中断")
    except Exception as e:
        print(f"\n测试过程中出现异常: {e}")
        traceback.print_exc()
