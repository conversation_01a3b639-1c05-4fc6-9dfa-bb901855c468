#!/usr/bin/env python3
"""
测试导入是否正常
"""

def test_imports():
    """测试所有关键模块的导入"""
    try:
        print("测试基础导入...")
        from fastapi import APIRouter
        from pydantic import BaseModel
        print("✓ FastAPI 和 Pydantic 导入成功")
        
        print("测试模型导入...")
        from Models.agent.ResumeInfo import ResumeInfo
        from Models.dto.TaskInfoDto import TaskInfoDto
        from Models.AjaxResult import AjaxResult
        print("✓ 模型类导入成功")
        
        print("测试Agent导入...")
        from Agents.TalentAgent import TalentAgent
        from Agents.TalentAgentProcessing import TalentAgentProcessing
        from Agents.ImproveInfoAgent import ImproveInfoAgent
        print("✓ 原始Agent类导入成功")
        
        print("测试异步Agent导入...")
        from Agents.AsyncTalentAgent import AsyncTalentAgent, async_talent_agent
        from Agents.AsyncImproveInfoAgent import AsyncImproveInfoAgent, async_improve_info_agent
        print("✓ 异步Agent类导入成功")
        
        print("测试服务导入...")
        from Services.TaskServer.TalentParseWorker import TalentParseWorker
        from Utils.FileDownloader import FileDownloader
        print("✓ 服务类导入成功")
        
        print("测试控制器导入...")
        from Controller.TalentInfoController import router
        print("✓ 控制器导入成功")
        
        print("\n🎉 所有导入测试通过！")
        return True
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        return False
    except Exception as e:
        print(f"❌ 其他错误: {e}")
        return False


def test_async_agent_creation():
    """测试异步代理的创建"""
    try:
        print("\n测试异步代理创建...")
        from Agents.AsyncTalentAgent import AsyncTalentAgent
        from Agents.AsyncImproveInfoAgent import AsyncImproveInfoAgent
        
        # 创建实例
        talent_agent = AsyncTalentAgent()
        improve_agent = AsyncImproveInfoAgent()
        
        print("✓ 异步代理创建成功")
        
        # 测试清理
        talent_agent.cleanup()
        improve_agent.cleanup()
        print("✓ 资源清理成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 异步代理创建失败: {e}")
        return False


def test_file_downloader():
    """测试文件下载器"""
    try:
        print("\n测试文件下载器...")
        from Utils.FileDownloader import FileDownloader
        
        # 创建实例
        downloader = FileDownloader("./temp_test")
        print("✓ 文件下载器创建成功")
        
        # 测试清理
        downloader.cleanup()
        print("✓ 文件下载器清理成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 文件下载器测试失败: {e}")
        return False


if __name__ == "__main__":
    print("开始测试修复后的代码...")
    print("=" * 50)
    
    success = True
    success &= test_imports()
    success &= test_async_agent_creation()
    success &= test_file_downloader()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 所有测试通过！代码修复成功！")
    else:
        print("❌ 部分测试失败，请检查错误信息")
