#!/usr/bin/env python3
"""
异步改进测试启动脚本
用于验证异步改进的效果
"""
import subprocess
import sys
import time
import os


def check_dependencies():
    """检查依赖是否安装"""
    print("检查依赖...")
    
    required_packages = ['aiohttp', 'fastapi', 'uvicorn']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ {package} 已安装")
        except ImportError:
            missing_packages.append(package)
            print(f"❌ {package} 未安装")
    
    if missing_packages:
        print(f"\n请安装缺失的依赖:")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    
    return True


def start_server():
    """启动服务器"""
    print("\n启动服务器...")
    
    try:
        # 启动服务器进程
        process = subprocess.Popen(
            [sys.executable, "Server.py"],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        print("服务器启动中，等待5秒...")
        time.sleep(5)
        
        # 检查进程是否还在运行
        if process.poll() is None:
            print("✅ 服务器启动成功")
            return process
        else:
            stdout, stderr = process.communicate()
            print(f"❌ 服务器启动失败")
            print(f"stdout: {stdout}")
            print(f"stderr: {stderr}")
            return None
            
    except Exception as e:
        print(f"❌ 启动服务器时出错: {e}")
        return None


def run_tests():
    """运行测试"""
    print("\n运行基本功能测试...")
    
    try:
        result = subprocess.run(
            [sys.executable, "test/test_basic_functionality.py"],
            capture_output=True,
            text=True,
            timeout=60
        )
        
        print(result.stdout)
        if result.stderr:
            print("错误输出:")
            print(result.stderr)
        
        return result.returncode == 0
        
    except subprocess.TimeoutExpired:
        print("❌ 测试超时")
        return False
    except Exception as e:
        print(f"❌ 运行测试时出错: {e}")
        return False


def main():
    """主函数"""
    print("异步改进测试启动器")
    print("=" * 50)
    
    # 检查依赖
    if not check_dependencies():
        print("\n请先安装依赖后再运行测试")
        return
    
    # 检查配置文件
    if not os.path.exists("Configs/config.json"):
        print("❌ 配置文件不存在: Configs/config.json")
        return
    
    print("✅ 配置文件存在")
    
    # 启动服务器
    server_process = start_server()
    if not server_process:
        print("无法启动服务器，测试终止")
        return
    
    try:
        # 运行测试
        test_success = run_tests()
        
        if test_success:
            print("\n🎉 测试完成！异步改进功能正常工作。")
        else:
            print("\n⚠️  测试中发现问题，请检查日志。")
    
    finally:
        # 关闭服务器
        print("\n关闭服务器...")
        server_process.terminate()
        try:
            server_process.wait(timeout=10)
            print("✅ 服务器已关闭")
        except subprocess.TimeoutExpired:
            print("强制关闭服务器...")
            server_process.kill()
            server_process.wait()
    
    print("\n测试完成！")
    print("\n使用说明:")
    print("1. 如果测试通过，说明异步改进已生效")
    print("2. 现在可以同时处理多个简历解析请求")
    print("3. 其他接口不会被简历解析阻塞")
    print("4. 可以使用新的异步接口: /agentService/api/talent/parse-async")


if __name__ == "__main__":
    main()
