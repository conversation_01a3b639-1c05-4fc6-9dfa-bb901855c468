#!/usr/bin/env python3
"""
测试非阻塞接口
验证新的任务管理器是否能解决阻塞问题
"""

import asyncio
import aiohttp
import time
import json


async def test_version_endpoint(session: aiohttp.ClientSession, base_url: str) -> dict:
    """测试version接口"""
    url = f"{base_url}/agentService/api/conversation/version"
    start_time = time.time()
    
    try:
        async with session.get(url) as response:
            result = await response.json()
            end_time = time.time()
            return {
                "success": True,
                "response_time": end_time - start_time,
                "status": response.status,
                "result": result
            }
    except Exception as e:
        end_time = time.time()
        return {
            "success": False,
            "response_time": end_time - start_time,
            "error": str(e)
        }


async def test_rating_endpoint(session: aiohttp.ClientSession, base_url: str) -> dict:
    """测试rating接口"""
    url = f"{base_url}/agentService/api/talent/rating"
    start_time = time.time()
    
    try:
        async with session.get(url) as response:
            result = await response.json()
            end_time = time.time()
            return {
                "success": True,
                "response_time": end_time - start_time,
                "status": response.status,
                "result": result
            }
    except Exception as e:
        end_time = time.time()
        return {
            "success": False,
            "response_time": end_time - start_time,
            "error": str(e)
        }


async def submit_parse_task(session: aiohttp.ClientSession, base_url: str) -> dict:
    """提交解析任务"""
    url = f"{base_url}/agentService/api/talent/parse-async"
    data = {"fileUrl": "http://example.com/test.pdf"}
    start_time = time.time()
    
    try:
        async with session.post(url, json=data) as response:
            result = await response.json()
            end_time = time.time()
            return {
                "success": True,
                "response_time": end_time - start_time,
                "status": response.status,
                "result": result
            }
    except Exception as e:
        end_time = time.time()
        return {
            "success": False,
            "response_time": end_time - start_time,
            "error": str(e)
        }


async def check_task_status(session: aiohttp.ClientSession, base_url: str, task_id: str) -> dict:
    """检查任务状态"""
    url = f"{base_url}/agentService/api/talent/task-status/{task_id}"
    start_time = time.time()
    
    try:
        async with session.get(url) as response:
            result = await response.json()
            end_time = time.time()
            return {
                "success": True,
                "response_time": end_time - start_time,
                "status": response.status,
                "result": result
            }
    except Exception as e:
        end_time = time.time()
        return {
            "success": False,
            "response_time": end_time - start_time,
            "error": str(e)
        }


async def non_blocking_test(base_url: str = "http://localhost:8000"):
    """非阻塞测试"""
    print("🚀 开始非阻塞接口测试...")
    print("=" * 60)
    
    async with aiohttp.ClientSession() as session:
        # 1. 提交一个解析任务
        print("1. 提交简历解析任务...")
        parse_result = await submit_parse_task(session, base_url)
        
        if not parse_result['success']:
            print(f"❌ 提交任务失败: {parse_result.get('error')}")
            return
            
        task_id = parse_result['result']['data'].get('task_id')
        if not task_id:
            print("❌ 未获取到task_id")
            return
            
        print(f"✅ 任务提交成功，task_id: {task_id}")
        print(f"   响应时间: {parse_result['response_time']:.3f}s")
        
        # 2. 立即测试其他接口是否能正常访问
        print("\n2. 测试其他接口是否能正常访问...")
        
        # 并发访问多个简单接口
        concurrent_tasks = []
        for i in range(10):  # 创建10个并发请求
            concurrent_tasks.append(test_version_endpoint(session, base_url))
            concurrent_tasks.append(test_rating_endpoint(session, base_url))
        
        start_time = time.time()
        concurrent_results = await asyncio.gather(*concurrent_tasks, return_exceptions=True)
        concurrent_time = time.time() - start_time
        
        # 分析并发结果
        successful_requests = [r for r in concurrent_results if isinstance(r, dict) and r.get('success', False)]
        failed_requests = [r for r in concurrent_results if not (isinstance(r, dict) and r.get('success', False))]
        
        print(f"   并发请求结果: {len(successful_requests)}/{len(concurrent_tasks)} 成功")
        print(f"   总耗时: {concurrent_time:.3f}s")
        
        if successful_requests:
            avg_time = sum(r['response_time'] for r in successful_requests) / len(successful_requests)
            max_time = max(r['response_time'] for r in successful_requests)
            print(f"   平均响应时间: {avg_time:.3f}s")
            print(f"   最大响应时间: {max_time:.3f}s")
        
        # 3. 检查解析任务状态
        print(f"\n3. 检查解析任务状态...")
        for i in range(5):  # 最多检查5次
            await asyncio.sleep(1)  # 等待1秒
            status_result = await check_task_status(session, base_url, task_id)
            
            if status_result['success']:
                task_status = status_result['result']['data']
                print(f"   第{i+1}次检查: {task_status['status']} (进度: {task_status.get('progress', 0)*100:.1f}%)")
                
                if task_status['status'] in ['completed', 'failed']:
                    break
            else:
                print(f"   第{i+1}次检查失败: {status_result.get('error')}")
        
        # 4. 评估结果
        print(f"\n4. 测试结果评估:")
        if len(successful_requests) == len(concurrent_tasks):
            if concurrent_time < 1.0:
                print("✅ 优秀：所有接口都能正常访问，响应迅速")
                print("✅ 非阻塞优化成功！")
            else:
                print("⚠️  一般：接口能访问但响应较慢")
        else:
            print("❌ 差：部分接口无法访问，仍存在阻塞问题")
            
        if failed_requests:
            print(f"\n失败请求详情:")
            for i, req in enumerate(failed_requests[:3]):
                if isinstance(req, dict):
                    print(f"  {i+1}. {req.get('error', 'Unknown error')}")
                else:
                    print(f"  {i+1}. Exception: {str(req)}")


async def main():
    """主函数"""
    print("🧪 FastAPI 非阻塞接口测试")
    print("测试目标：验证新的任务管理器是否解决了阻塞问题")
    print()
    
    try:
        await non_blocking_test()
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n" + "=" * 60)
    print("测试完成！")


if __name__ == "__main__":
    asyncio.run(main())
