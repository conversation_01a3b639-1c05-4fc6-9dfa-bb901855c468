#!/usr/bin/env python3
"""
优化后的服务器测试脚本
测试并发性能和接口响应能力
"""

import asyncio
import aiohttp
import time
import json
import sys
import subprocess
from concurrent.futures import ThreadPoolExecutor


async def test_basic_endpoint():
    """测试基础接口响应"""
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get("http://localhost:8000/agentService/api/llmSetting/list") as response:
                if response.status == 200:
                    print("✅ 基础接口响应正常")
                    return True
                else:
                    print(f"❌ 基础接口响应异常: {response.status}")
                    return False
    except Exception as e:
        print(f"❌ 基础接口测试失败: {e}")
        return False


async def test_concurrent_basic_requests():
    """测试并发基础请求"""
    print("\n🔄 测试并发基础请求...")
    
    async def single_request(session, request_id):
        try:
            start_time = time.time()
            async with session.get("http://localhost:8000/agentService/api/llmSetting/list") as response:
                end_time = time.time()
                duration = end_time - start_time
                if response.status == 200:
                    print(f"✅ 请求 {request_id}: {duration:.2f}s")
                    return True, duration
                else:
                    print(f"❌ 请求 {request_id}: 状态码 {response.status}")
                    return False, duration
        except Exception as e:
            end_time = time.time()
            duration = end_time - start_time
            print(f"❌ 请求 {request_id}: 异常 {e}")
            return False, duration

    async with aiohttp.ClientSession() as session:
        tasks = []
        for i in range(10):  # 10个并发请求
            tasks.append(single_request(session, i + 1))
        
        start_time = time.time()
        results = await asyncio.gather(*tasks, return_exceptions=True)
        end_time = time.time()
        
        successful = sum(1 for result in results if isinstance(result, tuple) and result[0])
        total_time = end_time - start_time
        
        print(f"📊 并发测试结果: {successful}/10 成功, 总耗时: {total_time:.2f}s")
        return successful >= 8  # 80%成功率


async def test_resume_parse_non_blocking():
    """测试简历解析是否阻塞其他接口"""
    print("\n🔄 测试简历解析非阻塞性...")
    
    async def parse_request(session):
        """发起简历解析请求"""
        try:
            # 使用一个无效的URL来模拟解析请求（会快速失败）
            url = "http://localhost:8000/agentService/api/talent/parse?fileUrl=http://invalid-url.com/test.pdf"
            async with session.get(url) as response:
                return response.status
        except Exception as e:
            print(f"解析请求异常（预期）: {e}")
            return None

    async def basic_request(session, request_id):
        """发起基础请求"""
        try:
            start_time = time.time()
            async with session.get("http://localhost:8000/agentService/api/llmSetting/list") as response:
                end_time = time.time()
                duration = end_time - start_time
                if response.status == 200:
                    print(f"✅ 基础请求 {request_id}: {duration:.2f}s (在解析期间)")
                    return True, duration
                else:
                    print(f"❌ 基础请求 {request_id}: 状态码 {response.status}")
                    return False, duration
        except Exception as e:
            end_time = time.time()
            duration = end_time - start_time
            print(f"❌ 基础请求 {request_id}: 异常 {e}")
            return False, duration

    async with aiohttp.ClientSession() as session:
        # 启动解析请求
        parse_task = asyncio.create_task(parse_request(session))
        
        # 等待一点时间让解析请求开始
        await asyncio.sleep(0.5)
        
        # 同时发起多个基础请求
        basic_tasks = []
        for i in range(5):
            basic_tasks.append(basic_request(session, i + 1))
        
        # 等待所有任务完成
        basic_results = await asyncio.gather(*basic_tasks, return_exceptions=True)
        await parse_task
        
        successful = sum(1 for result in basic_results if isinstance(result, tuple) and result[0])
        print(f"📊 非阻塞测试结果: {successful}/5 基础请求成功")
        
        return successful >= 4  # 80%成功率


async def test_task_manager_api():
    """测试任务管理器API"""
    print("\n🔄 测试任务管理器API...")
    
    try:
        async with aiohttp.ClientSession() as session:
            # 提交一个异步解析任务
            data = {"fileUrl": "http://invalid-url.com/test.pdf"}
            async with session.post(
                "http://localhost:8000/agentService/api/talent/parse-async",
                json=data,
                headers={"Content-Type": "application/json"}
            ) as response:
                if response.status == 200:
                    result = await response.json()
                    if result.get("success") and "task_id" in result.get("data", {}):
                        task_id = result["data"]["task_id"]
                        print(f"✅ 任务提交成功: {task_id}")
                        
                        # 查询任务状态
                        await asyncio.sleep(1)
                        async with session.get(f"http://localhost:8000/agentService/api/talent/task-status/{task_id}") as status_response:
                            if status_response.status == 200:
                                status_result = await status_response.json()
                                print(f"✅ 任务状态查询成功: {status_result}")
                                return True
                            else:
                                print(f"❌ 任务状态查询失败: {status_response.status}")
                                return False
                    else:
                        print(f"❌ 任务提交响应格式错误: {result}")
                        return False
                else:
                    print(f"❌ 任务提交失败: {response.status}")
                    return False
    except Exception as e:
        print(f"❌ 任务管理器API测试失败: {e}")
        return False


async def run_all_tests():
    """运行所有测试"""
    print("🚀 开始优化后的服务器测试")
    print("=" * 50)
    
    # 等待服务器启动
    print("⏳ 等待服务器启动...")
    await asyncio.sleep(3)
    
    tests = [
        ("基础接口测试", test_basic_endpoint),
        ("并发基础请求测试", test_concurrent_basic_requests),
        ("简历解析非阻塞测试", test_resume_parse_non_blocking),
        ("任务管理器API测试", test_task_manager_api),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n📋 执行: {test_name}")
        try:
            result = await test_func()
            results.append((test_name, result))
            if result:
                print(f"✅ {test_name}: 通过")
            else:
                print(f"❌ {test_name}: 失败")
        except Exception as e:
            print(f"❌ {test_name}: 异常 - {e}")
            results.append((test_name, False))
    
    # 总结
    print("\n" + "=" * 50)
    print("📊 测试结果总结:")
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
    
    print(f"\n🎯 总体结果: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！服务器优化成功！")
    elif passed >= total * 0.8:
        print("⚠️  大部分测试通过，服务器基本正常")
    else:
        print("❌ 多个测试失败，需要进一步优化")
    
    return passed >= total * 0.8


def start_server():
    """启动服务器"""
    print("🚀 启动优化后的服务器...")
    try:
        process = subprocess.Popen(
            [sys.executable, "Server.py"],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        return process
    except Exception as e:
        print(f"❌ 启动服务器失败: {e}")
        return None


async def main():
    """主函数"""
    server_process = start_server()
    if not server_process:
        print("无法启动服务器")
        return
    
    try:
        success = await run_all_tests()
        return success
    finally:
        print("\n🔄 关闭服务器...")
        server_process.terminate()
        try:
            server_process.wait(timeout=10)
        except subprocess.TimeoutExpired:
            server_process.kill()
            server_process.wait()
        print("✅ 服务器已关闭")


if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⚠️  测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 测试过程中发生异常: {e}")
        sys.exit(1)
