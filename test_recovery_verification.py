#!/usr/bin/env python3
"""
验证代码恢复是否成功
"""

def test_imports():
    """测试所有关键模块的导入"""
    try:
        print("测试基础导入...")
        from fastapi import APIRouter
        from pydantic import BaseModel
        print("✓ FastAPI 和 Pydantic 导入成功")
        
        print("测试模型导入...")
        from Models.agent.ResumeInfo import ResumeInfo
        from Models.dto.TaskInfoDto import TaskInfoDto
        from Models.AjaxResult import AjaxResult
        print("✓ 模型类导入成功")
        
        print("测试Agent导入...")
        from Agents.TalentAgent import TalentAgent
        from Agents.TalentAgentProcessing import TalentAgentProcessing
        from Agents.ImproveInfoAgent import ImproveInfoAgent
        print("✓ 原始Agent类导入成功")
        
        print("测试异步Agent导入...")
        from Agents.AsyncTalentAgent import AsyncTalentAgent, async_talent_agent
        from Agents.AsyncImproveInfoAgent import AsyncImproveInfoAgent, async_improve_info_agent
        print("✓ 异步Agent类导入成功")
        
        print("测试任务管理器导入...")
        from Utils.TaskManager import TaskManager, task_manager
        print("✓ 任务管理器导入成功")
        
        print("测试服务导入...")
        from Services.TaskServer.TalentParseWorker import TalentParseWorker
        from Utils.FileDownloader import FileDownloader
        print("✓ 服务类导入成功")
        
        print("测试控制器导入...")
        from Controller.TalentInfoController import router
        print("✓ 控制器导入成功")
        
        print("\n🎉 所有导入测试通过！")
        return True
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        return False
    except Exception as e:
        print(f"❌ 其他错误: {e}")
        return False


def test_async_agent_creation():
    """测试异步代理的创建"""
    try:
        print("\n测试异步代理创建...")
        from Agents.AsyncTalentAgent import AsyncTalentAgent
        from Agents.AsyncImproveInfoAgent import AsyncImproveInfoAgent
        
        # 创建实例
        talent_agent = AsyncTalentAgent()
        improve_agent = AsyncImproveInfoAgent()
        
        print("✓ 异步代理创建成功")
        
        # 测试清理
        talent_agent.cleanup()
        improve_agent.cleanup()
        print("✓ 资源清理成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 异步代理创建失败: {e}")
        return False


def test_task_manager():
    """测试任务管理器"""
    try:
        print("\n测试任务管理器...")
        from Utils.TaskManager import TaskManager
        
        # 创建实例
        tm = TaskManager(max_workers=2)
        print("✓ 任务管理器创建成功")
        
        # 测试任务提交
        def test_task():
            return "test result"
        
        task_id = tm.submit_task(test_task)
        print(f"✓ 任务提交成功，task_id: {task_id}")
        
        # 等待一下
        import time
        time.sleep(1)
        
        # 检查状态
        status = tm.get_task_status(task_id)
        if status:
            print(f"✓ 任务状态查询成功: {status['status']}")
        
        # 清理
        tm.executor.shutdown(wait=True)
        print("✓ 任务管理器清理成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 任务管理器测试失败: {e}")
        return False


def test_file_downloader():
    """测试文件下载器"""
    try:
        print("\n测试文件下载器...")
        from Utils.FileDownloader import FileDownloader
        
        # 创建实例
        downloader = FileDownloader("./temp_test")
        print("✓ 文件下载器创建成功")
        
        # 检查是否有异步方法
        if hasattr(downloader, 'download_async'):
            print("✓ 异步下载方法存在")
        
        if hasattr(downloader, 'download_in_thread'):
            print("✓ 线程池下载方法存在")
        
        # 测试清理
        downloader.cleanup()
        print("✓ 文件下载器清理成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 文件下载器测试失败: {e}")
        return False


def test_controller_endpoints():
    """测试控制器端点"""
    try:
        print("\n测试控制器端点...")
        from Controller.TalentInfoController import router
        
        # 检查路由
        routes = [route.path for route in router.routes]
        expected_routes = ["/info", "/parse", "/parseDto", "/analyse", "/improveInfo", "/parse-async", "/task-status/{task_id}"]
        
        for route in expected_routes:
            if any(route in r for r in routes):
                print(f"✓ 路由 {route} 存在")
            else:
                print(f"⚠️  路由 {route} 可能不存在")
        
        print("✓ 控制器端点检查完成")
        return True
        
    except Exception as e:
        print(f"❌ 控制器端点测试失败: {e}")
        return False


def main():
    """主函数"""
    print("🔍 代码恢复验证测试")
    print("=" * 60)
    
    success = True
    success &= test_imports()
    success &= test_async_agent_creation()
    success &= test_task_manager()
    success &= test_file_downloader()
    success &= test_controller_endpoints()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 所有测试通过！代码恢复成功！")
        print("\n📋 恢复的功能包括:")
        print("  ✅ 异步TalentAgent和ImproveInfoAgent")
        print("  ✅ 任务管理器（TaskManager）")
        print("  ✅ 异步文件下载器")
        print("  ✅ 非阻塞的简历解析接口")
        print("  ✅ 改进的现有接口（带超时机制）")
        print("\n🚀 现在可以启动服务测试并发性能了！")
    else:
        print("❌ 部分测试失败，请检查错误信息")


if __name__ == "__main__":
    main()
