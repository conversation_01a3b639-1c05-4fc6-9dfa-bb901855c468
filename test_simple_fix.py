#!/usr/bin/env python3
"""
简单的修复验证脚本
"""
import asyncio
import aiohttp
import json


async def test_talent_info_simple():
    """简单测试 talent info 接口"""
    print("测试 /talent/info 接口...")
    
    test_data = {
        "question": "你好",
        "id": 1
    }
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.post(
                "http://localhost:8000/agentService/api/talent/info",
                json=test_data,
                timeout=aiohttp.ClientTimeout(total=10)
            ) as response:
                print(f"响应状态: {response.status}")
                
                if response.status == 200:
                    try:
                        result = await response.json()
                        print(f"响应成功: {json.dumps(result, indent=2, ensure_ascii=False)}")
                        return True
                    except Exception as e:
                        text = await response.text()
                        print(f"JSON解析失败: {e}")
                        print(f"原始响应: {text}")
                        return False
                else:
                    text = await response.text()
                    print(f"请求失败: {text}")
                    return False
                    
    except Exception as e:
        print(f"请求异常: {e}")
        return False


def test_talent_agent_import():
    """测试TalentAgent导入和创建"""
    print("测试TalentAgent导入...")
    
    try:
        from Agents.TalentAgent import TalentAgent
        print("✅ TalentAgent 导入成功")
        
        agent = TalentAgent()
        print("✅ TalentAgent 实例创建成功")
        
        # 测试新方法是否存在
        if hasattr(agent, 'chat_for_question'):
            print("✅ chat_for_question 方法存在")
        else:
            print("❌ chat_for_question 方法不存在")
            
        return True
        
    except Exception as e:
        print(f"❌ TalentAgent 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_talent_agent_method():
    """测试TalentAgent的chat_for_question方法"""
    print("测试TalentAgent.chat_for_question方法...")
    
    try:
        from Agents.TalentAgent import TalentAgent
        agent = TalentAgent()
        
        result = await agent.chat_for_question("你好", 1)
        print(f"✅ 方法调用成功，结果: {result}")
        return True
        
    except Exception as e:
        print(f"❌ 方法调用失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """主测试函数"""
    print("=" * 50)
    print("简单修复验证测试")
    print("=" * 50)
    
    # 1. 测试导入
    import_ok = test_talent_agent_import()
    
    if import_ok:
        # 2. 测试方法
        method_ok = await test_talent_agent_method()
        
        if method_ok:
            # 3. 测试接口
            api_ok = await test_talent_info_simple()
            
            if api_ok:
                print("\n🎉 所有测试通过！")
            else:
                print("\n⚠️  接口测试失败")
        else:
            print("\n⚠️  方法测试失败")
    else:
        print("\n⚠️  导入测试失败")
    
    print("\n" + "=" * 50)


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except Exception as e:
        print(f"测试异常: {e}")
        import traceback
        traceback.print_exc()
