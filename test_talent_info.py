#!/usr/bin/env python3
"""
专门测试 /talent/info 接口的脚本
"""
import asyncio
import aiohttp
import json
import traceback


async def test_talent_info_step_by_step():
    """逐步测试 talent info 接口"""
    print("开始逐步测试 /talent/info 接口...")
    
    # 测试数据
    test_cases = [
        {
            "name": "简单问候",
            "data": {"question": "你好", "id": 1}
        },
        {
            "name": "空问题测试",
            "data": {"question": "", "id": 1}
        },
        {
            "name": "无ID测试",
            "data": {"question": "你好"}
        }
    ]
    
    async with aiohttp.ClientSession() as session:
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n{i}. 测试: {test_case['name']}")
            print(f"请求数据: {test_case['data']}")
            
            try:
                async with session.post(
                    "http://localhost:8000/agentService/api/talent/info",
                    json=test_case['data'],
                    timeout=aiohttp.ClientTimeout(total=30)
                ) as response:
                    print(f"响应状态: {response.status}")
                    
                    if response.status == 200:
                        try:
                            result = await response.json()
                            print(f"响应内容: {json.dumps(result, indent=2, ensure_ascii=False)}")
                            print("✅ 测试成功")
                        except Exception as e:
                            text = await response.text()
                            print(f"JSON解析失败: {e}")
                            print(f"原始响应: {text}")
                            print("❌ JSON解析失败")
                    else:
                        text = await response.text()
                        print(f"错误响应: {text}")
                        print("❌ 请求失败")
                        
            except asyncio.TimeoutError:
                print("❌ 请求超时")
            except Exception as e:
                print(f"❌ 请求异常: {e}")
                traceback.print_exc()


def test_imports():
    """测试相关模块的导入"""
    print("测试模块导入...")
    
    try:
        from Agents.TalentAgent import TalentAgent
        print("✅ TalentAgent 导入成功")
        
        # 尝试创建实例
        agent = TalentAgent()
        print("✅ TalentAgent 实例创建成功")
        
    except Exception as e:
        print(f"❌ TalentAgent 导入/创建失败: {e}")
        traceback.print_exc()
    
    try:
        from Agents.TalentAgentProcessing import TalentAgentProcessing
        print("✅ TalentAgentProcessing 导入成功")
        
        # 尝试创建实例
        processor = TalentAgentProcessing()
        print("✅ TalentAgentProcessing 实例创建成功")
        
    except Exception as e:
        print(f"❌ TalentAgentProcessing 导入/创建失败: {e}")
        traceback.print_exc()
    
    try:
        from Configs.Config import SysConfig
        print("✅ SysConfig 导入成功")
        
        # 检查关键配置
        print(f"配置检查:")
        print(f"  - agents: {SysConfig.get('agents', 'NOT_FOUND')}")
        print(f"  - talent: {SysConfig.get('talent', 'NOT_FOUND')}")
        
    except Exception as e:
        print(f"❌ SysConfig 导入失败: {e}")
        traceback.print_exc()
    
    try:
        from Utils.LLMManager.SysLLMManager import sys_llm_manager
        print("✅ sys_llm_manager 导入成功")
        
        # 检查LLM管理器
        llm_helper = sys_llm_manager.get_generate_use_llm_helper()
        if llm_helper:
            print("✅ LLM助手获取成功")
        else:
            print("⚠️  LLM助手为空")
            
    except Exception as e:
        print(f"❌ sys_llm_manager 导入失败: {e}")
        traceback.print_exc()


def test_config():
    """测试配置文件"""
    print("\n测试配置文件...")
    
    try:
        from Configs.Config import SysConfig
        
        # 检查必要的配置项
        required_configs = [
            ("agents.kb_agent.temperature", ["agents", "kb_agent", "temperature"]),
            ("talent.talent_url", ["talent", "talent_url"]),
            ("talent.tokens", ["talent", "tokens"]),
            ("talent.match_score", ["talent", "match_score"])
        ]
        
        for config_name, config_path in required_configs:
            try:
                value = SysConfig
                for key in config_path:
                    value = value[key]
                print(f"✅ {config_name}: {value}")
            except (KeyError, TypeError):
                print(f"❌ {config_name}: 配置缺失")
                
    except Exception as e:
        print(f"❌ 配置测试失败: {e}")
        traceback.print_exc()


async def test_server_connection():
    """测试服务器连接"""
    print("\n测试服务器连接...")
    
    try:
        async with aiohttp.ClientSession() as session:
            # 测试基本连接
            async with session.get("http://localhost:8000/docs") as response:
                if response.status == 200:
                    print("✅ 服务器连接正常")
                    return True
                else:
                    print(f"❌ 服务器响应异常: {response.status}")
                    return False
    except Exception as e:
        print(f"❌ 无法连接到服务器: {e}")
        return False


async def main():
    """主测试函数"""
    print("=" * 60)
    print("TalentInfo 接口专项测试")
    print("=" * 60)
    
    # 1. 测试模块导入
    test_imports()
    
    # 2. 测试配置
    test_config()
    
    # 3. 测试服务器连接
    server_ok = await test_server_connection()
    
    if server_ok:
        # 4. 测试接口
        await test_talent_info_step_by_step()
    else:
        print("\n服务器连接失败，跳过接口测试")
    
    print("\n" + "=" * 60)
    print("测试完成")
    print("=" * 60)
    
    print("\n故障排除建议:")
    print("1. 如果模块导入失败，检查Python路径和依赖")
    print("2. 如果配置缺失，检查 Configs/config.json 文件")
    print("3. 如果LLM助手为空，检查LLM服务是否启动")
    print("4. 如果接口超时，检查LLM模型是否正常工作")
    print("5. 查看服务器日志获取详细错误信息")


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n测试被用户中断")
    except Exception as e:
        print(f"\n测试过程中出现异常: {e}")
        traceback.print_exc()
